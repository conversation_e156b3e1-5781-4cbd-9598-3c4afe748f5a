{"paper_id": "W2314772071", "TP": 20, "FP": 15, "FN": 23, "precision": 0.571, "recall": 0.465, "f1": 0.513, "llm_vs_human_corr_overall": -0.14285714285714285, "llm_vs_human_corr_by_endpoint": {"ANTIGEN_EXPRESSION": -1.0, "ADC_IC50": -1.0, "ANTI_TUMOR_ACTIVITY_DOSE": 1.0, "TUMOR_GROWTH_INHIBITION": -1.0, "TOXICOLOGY_DOSE": -1.0, "LETHAL_DOSE": 0.0, "DECREASED_FOOD_CONSUMPTION": 1.0, "GI_ISSUES": 1.0, "DECREASED_BODY_WEIGHT": 1.0, "REDUCED_RED_BLOOD_COUNT": 1.0, "WHITE_BLOOD_CELLS": 1.0, "TARGET_ORGAN_ISSUES": 1.0, "PK_DOSE": 1.0, "HNSTD": 1.0, "LUNG_INFLAMMATION": -1.0}, "detailed_results": [{"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "NCI-N87", "model_type": null, "experiment_type": null, "endpoint_name": "ANTIGEN_EXPRESSION", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "In Vitro Studies", "model_type": "Cell Line Model", "citations": ["HER2 expression on the cell surface of the cell lines KPL-4, NCI-N87, SK-BR-3, and MDA-MB-468 was firstly evaluated by flow cytometric analysis (Fig. 2A). The relative MFIs of KPL-4, NCI-N87, and SK-BR-3 were 95.7, 101.6, and 56.2, respectively, suggesting that HER2 is clearly expressed on the cell surfaces, whereas the relative MFI was 1.0 for MDA-MB-468, indicating no expression in the MDA-MB-468."], "measured_value": "High Antigen Expression", "measured_time": null, "measured_concentration": null}]}, "llm_response": {"matched": true, "gt_id": "GT_ROW_13", "matched_gt_endpoint": {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "NCI-N87", "model_type": "Cell Line", "experiment_type": "In Vitro", "endpoint_name": "Antigen Expression (Biomarker High or Low Model)", "endpoint_type": "Biomarker", "endpoint_value": "High Antigen Expression", "endpoint_units": "NR", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_13"}, "confidence": 0.95, "reason": "The extracted endpoint has ADC DS-8201a and model NCI-N87 with in vitro experiment type and antigen expression measurement. This closely matches GT_ROW_13 which has all the core details and the same reported antigen expression value.", "reasoning": "Fallback model used due to: LLM response parsing failed: 'Completions' object has no attribute 'parse'. Step 1: The ADC name DS-8201a and model name NCI-N87 match exactly. The measurement comes from an in vitro experiment, and the provided measurement details in the extraction (Cell Line Model, In Vitro Studies) align with GT_ROW_13’s 'Cell Line' and 'In Vitro' designation. Step 2: The endpoint name in the extraction 'ANTIGEN_EXPRESSION' is equivalent to 'Antigen Expression (Biomarker High or Low Model)' in GT_ROW_13, both targeting antigen expression status. Step 3: The measured value 'High Antigen Expression' in the extraction exactly matches the ground truth's endpoint value. Thus, GT_ROW_13 is the best matching endpoint.", "reasoning_tokens": 512, "language_model": "o3-mini"}, "classification": "TP", "extraction_row_index": 0, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "NCI-N87", "model_type": null, "experiment_type": null, "endpoint_name": "ADC_IC50", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "In Vitro Studies", "model_type": "Cell Line Model", "citations": ["Remarkable inhibitory activity to the cell growth was observed for DS-8201a against HER2-positive KPL-4, NCI-N87, and SK-BR-3, with the IC50 values of 26.8, 25.4, and 6.7 ng/mL, respectively, whereas no such inhibition was seen against MDA-MB-468 with the IC50 value of >10,000 ng/mL (Fig. 2B)."], "measured_value": "25.4 ng/mL", "measured_time": "6 days", "measured_concentration": null}]}, "llm_response": {"matched": true, "gt_id": "GT_ROW_5", "matched_gt_endpoint": {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "NCI-N87", "model_type": "Cell Line", "experiment_type": "In Vitro", "endpoint_name": "ADC IC??", "endpoint_type": "Efficacy", "endpoint_value": "25.4", "endpoint_units": "ng/mL", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_5"}, "confidence": 1.0, "reason": "The extraction matches the ground truth with same ADC (DS-8201a) and model (NCI-N87), similar model type (Cell Line Model vs Cell Line) and experiment type (In Vitro Studies vs In Vitro), and the endpoint names (ADC_IC50 vs ADC IC??) and measured value (25.4 ng/mL) align perfectly.", "reasoning": "Fallback model used due to: LLM response parsing failed: 'Completions' object has no attribute 'parse'. Step 1: Verified ADC name and model name exactly match (DS-8201a and NCI-N87). Step 2: Confirmed similar model type and experiment type ('Cell Line Model' vs. 'Cell Line' and 'In Vitro Studies' vs 'In Vitro'). Step 3: The endpoint name 'ADC_IC50' is equivalent to 'ADC IC??' (likely representing the IC50 measurement) with matching measured value of 25.4 ng/mL. Therefore, GT_ROW_5 is the best match.", "reasoning_tokens": 512, "language_model": "o3-mini"}, "classification": "TP", "extraction_row_index": 1, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "NCI-N87", "model_type": null, "experiment_type": null, "endpoint_name": "ANTI_TUMOR_ACTIVITY_DOSE", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "In Vivo Studies", "model_type": "Cell Line-Derived Xenograft (CDX)", "citations": ["The in vivo antitumor activity of DS-8201a was evaluated in a HER2-positive NCI-N87 xenograft model. DS-8201a induced tumor growth inhibition in a dose-dependent manner and tumor regression with a single dosing at more than 1 mg/kg without inducing any abnormalities in the general condition or body weight changes of the mice (Fig. 2C).", "On the other hand, DS-8201a clearly showed more potent antitumor efficacy, indicating 99% TGI at the same dose of 4 mg/kg, so that the enhancement of efficacy by drug conjugation was observed in vivo as well as in the in vitro models (Fig. 2D)."], "measured_value": "1 mg/kg, 4 mg/kg", "measured_time": null, "measured_concentration": null}]}, "llm_response": {"matched": false, "gt_id": null, "matched_gt_endpoint": null, "confidence": 0.0, "reason": "Matching error: LLM response parsing failed: Both primary and fallback models failed to parse response", "reasoning": null, "reasoning_tokens": null, "language_model": null}, "classification": "FP", "extraction_row_index": 2, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "NCI-N87", "model_type": null, "experiment_type": null, "endpoint_name": "TUMOR_GROWTH_INHIBITION", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "In Vivo Studies", "model_type": "Cell Line-Derived Xenograft (CDX)", "citations": ["The in vivo antitumor activity of DS-8201a was evaluated in a HER2-positive NCI-N87 xenograft model. DS-8201a induced tumor growth inhibition in a dose-dependent manner and tumor regression with a single dosing at more than 1 mg/kg without inducing any abnormalities in the general condition or body weight changes of the mice (Fig. 2C). In the same model, 4 mg/kg administration of anti-HER2 Ab partially inhibited the tumor growth, indicating 31% of tumor growth inhibition (TGI) compared with the control group on day 21 (Fig. 2D). On the other hand, DS-8201a clearly showed more potent antitumor efficacy, indicating 99% TGI at the same dose of 4 mg/kg, so that the enhancement of efficacy by drug conjugation was observed in vivo as well as in the in vitro models (Fig. 2D)."], "measured_value": "99% TGI at 4 mg/kg", "measured_time": "day 21", "measured_concentration": null}]}, "llm_response": {"matched": true, "gt_id": "GT_ROW_42", "matched_gt_endpoint": {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "NCI-N87", "model_type": "Cell Line-Derived Xenograft (CDX)", "experiment_type": "In Vivo", "endpoint_name": "Tumor growth inhibition", "endpoint_type": "Efficacy", "endpoint_value": "99% tumor growth inhibition (TGI) at 4", "endpoint_units": "mg/kg", "endpoint_timepoint": "NR", "endpoint_concentration": "4 mg/kg", "gt_id": "GT_ROW_42"}, "confidence": 1.0, "reason": "Matched based on ADC 'DS-8201a', model 'NCI-N87' with CDX in an in vivo experiment. The endpoint 'TUMOR_GROWTH_INHIBITION' with value '99% TGI at 4 mg/kg' directly corresponds to GT_ROW_42.", "reasoning": "Fallback model used due to: LLM response parsing failed: 'Completions' object has no attribute 'parse'. Step 1: The ADC name DS-8201a and Model name NCI-N87 match directly. Step 2: The extracted measurement contains 'Cell Line-Derived Xenograft (CDX)' for model type and 'In Vivo Studies' for experiment type, which correspond to the CDX and In Vivo setting in GT_ROW_42. Step 3: The endpoint name 'TUMOR_GROWTH_INHIBITION' and the measured value '99% TGI at 4 mg/kg' are consistent with GT_ROW_42's 'Tumor growth inhibition', value '99% tumor growth inhibition (TGI) at 4' with mg/kg units. Thus, GT_ROW_42 is the best match.", "reasoning_tokens": 1024, "language_model": "o3-mini"}, "classification": "TP", "extraction_row_index": 3, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "SK-BR-3", "model_type": null, "experiment_type": null, "endpoint_name": "ANTIGEN_EXPRESSION", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "In Vitro Studies", "model_type": "Cell Line Model", "citations": ["HER2 expression on the cell surface of the cell lines KPL-4, NCI-N87, SK-BR-3, and MDA-MB-468 was firstly evaluated by flow cytometric analysis (Fig. 2A). The relative MFIs of KPL-4, NCI-N87, and SK-BR-3 were 95.7, 101.6, and 56.2, respectively, suggesting that HER2 is clearly expressed on the cell surfaces, whereas the relative MFI was 1.0 for MDA-MB-468, indicating no expression in the MDA-MB-468."], "measured_value": "High Antigen Expression", "measured_time": null, "measured_concentration": null}]}, "llm_response": {"matched": true, "gt_id": "GT_ROW_15", "matched_gt_endpoint": {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "SK-BR-3", "model_type": "Cell Line", "experiment_type": "In Vitro", "endpoint_name": "Antigen Expression (Biomarker High or Low Model)", "endpoint_type": "Biomarker", "endpoint_value": "High Antigen Expression", "endpoint_units": "NR", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_15"}, "confidence": 1.0, "reason": "Extracted endpoint for DS-8201a in SK-BR-3 with antigen expression measurement high matches GT_ROW_15.", "reasoning": "Fallback model used due to: LLM response parsing failed: 'Completions' object has no attribute 'parse'. The extracted endpoint has ADC name DS-8201a and model name SK-BR-3. The measurement is antigen expression with a value 'High Antigen Expression'. The extracted experiment is In Vitro and model type is a cell line, which aligns with the ground truth entry GT_ROW_15 where the endpoint name is 'Antigen Expression (Biomarker High or Low Model)', model type is 'Cell Line', and measured value is 'High Antigen Expression'. Therefore, GT_ROW_15 is the best match.", "reasoning_tokens": 384, "language_model": "o3-mini"}, "classification": "TP", "extraction_row_index": 4, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "SK-BR-3", "model_type": null, "experiment_type": null, "endpoint_name": "ADC_IC50", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "In Vitro Studies", "model_type": "Cell Line Model", "citations": ["Remarkable inhibitory activity to the cell growth was observed for DS-8201a against HER2-positive KPL-4, NCI-N87, and SK-BR-3, with the IC50 values of 26.8, 25.4, and 6.7 ng/mL, respectively, whereas no such inhibition was seen against MDA-MB-468 with the IC50 value of >10,000 ng/mL (Fig. 2B)."], "measured_value": "6.7 ng/mL", "measured_time": null, "measured_concentration": null}]}, "llm_response": {"matched": true, "gt_id": "GT_ROW_6", "matched_gt_endpoint": {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "SK-BR-3", "model_type": "Cell Line", "experiment_type": "In Vitro", "endpoint_name": "ADC IC??", "endpoint_type": "Efficacy", "endpoint_value": "6.7", "endpoint_units": "ng/mL", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_6"}, "confidence": 1.0, "reason": "The extraction and the ground truth both refer to the ADC DS-8201a tested in a SK-BR-3 cell line model using in vitro conditions. The endpoint measured is an IC50 value (extracted as ADC_IC50 vs. ADC IC?? in the ground truth) with a numerical value of 6.7 ng/mL. All core entities including the paper, ADC, model, experiment type, and numerical endpoint match.", "reasoning": "Fallback model used due to: LLM response parsing failed: 'Completions' object has no attribute 'parse'. Step 1: Both the extraction and GT_ROW_6 involve DS-8201a and the SK-BR-3 cell line. Step 2: The experimental conditions (In Vitro / Cell Line Studies) are equivalent. Step 3: The endpoint measured is a potency measurement (IC50) with an extracted value of 6.7 ng/mL and GT_ROW_6 reports 6.7 ng/mL under 'ADC IC??' which clearly corresponds to a similar measurement. Thus, GT_ROW_6 is the best match.", "reasoning_tokens": 640, "language_model": "o3-mini"}, "classification": "TP", "extraction_row_index": 5, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "MDA-MB-468", "model_type": null, "experiment_type": null, "endpoint_name": "ANTIGEN_EXPRESSION", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "In Vitro Studies", "model_type": "Cell Line Model", "citations": ["HER2 expression on the cell surface of the cell lines KPL-4, NCI-N87, SK-BR-3, and MDA-MB-468 was firstly evaluated by flow cytometric analysis (Fig. 2A). The relative MFIs of KPL-4, NCI-N87, and SK-BR-3 were 95.7, 101.6, and 56.2, respectively, suggesting that HER2 is clearly expressed on the cell surfaces, whereas the relative MFI was 1.0 for MDA-MB-468, indicating no expression in the MDA-MB-468."], "measured_value": "Low Antigen Expression", "measured_time": null, "measured_concentration": null}]}, "llm_response": {"matched": true, "gt_id": "GT_ROW_12", "matched_gt_endpoint": {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "MDA-MB-468", "model_type": "Cell Line", "experiment_type": "In Vitro", "endpoint_name": "Antigen Expression (Biomarker High or Low Model)", "endpoint_type": "Biomarker", "endpoint_value": "Low Antigen Expression", "endpoint_units": "NR", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_12"}, "confidence": 1.0, "reason": "The extracted endpoint has ADC name DS-8201a, model MDA-MB-468, In Vitro cell line experiment with a low antigen expression measurement. GT_ROW_12 exactly matches these core parameters and measurement values.", "reasoning": "Fallback model used due to: LLM response parsing failed: 'Completions' object has no attribute 'parse'. Step 1: The ADC names and model names match (DS-8201a and MDA-MB-468). Step 2: The experimental conditions match as well, with the extracted endpoint indicating an In Vitro cell line study, consistent with the ground truth's 'Cell Line' and 'In Vitro' settings. Step 3: The endpoint name, despite slight variation in phrasing ('ANTIGEN_EXPRESSION' vs 'Antigen Expression (Biomarker High or Low Model)'), represents the same measurement of antigen expression, and the endpoint values ('Low Antigen Expression') are identical. Therefore, GT_ROW_12 is the best matching ground truth endpoint.", "reasoning_tokens": 768, "language_model": "o3-mini"}, "classification": "TP", "extraction_row_index": 6, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "MDA-MB-468", "model_type": null, "experiment_type": null, "endpoint_name": "ADC_IC50", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "In Vitro Studies", "model_type": "Cell Line Model", "citations": ["Remarkable inhibitory activity to the cell growth was observed for DS-8201a against HER2-positive KPL-4, NCI-N87, and SK-BR-3, with the IC50 values of 26.8, 25.4, and 6.7 ng/mL, respectively, whereas no such inhibition was seen against MDA-MB-468 with the IC50 value of >10,000 ng/mL (Fig. 2B)."], "measured_value": ">10,000 ng/mL", "measured_time": "6 days", "measured_concentration": null}]}, "llm_response": {"matched": true, "gt_id": "GT_ROW_4", "matched_gt_endpoint": {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "MDA-MB-468", "model_type": "Cell Line", "experiment_type": "In Vitro", "endpoint_name": "ADC IC??", "endpoint_type": "Efficacy", "endpoint_value": ">10,000", "endpoint_units": "ng/mL", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_4"}, "confidence": 1.0, "reason": "Match found with GT_ROW_4.", "reasoning": "Fallback model used due to: LLM response parsing failed: 'Completions' object has no attribute 'parse'. Step 1: The ADC names match (DS-8201a) and the model names match (MDA-MB-468). Step 2: The extracted measurement is from an In Vitro cell line study, and GT_ROW_4 indicates a Cell Line with In Vitro study which is consistent. Step 3: The endpoint names (ADC_IC50 vs ADC IC??) are considered equivalent in context of efficacy measurements, and the measured value '>10,000 ng/mL' closely aligns with GT_ROW_4's value '>10,000' with matching units. Therefore, GT_ROW_4 is the best match.", "reasoning_tokens": 640, "language_model": "o3-mini"}, "classification": "TP", "extraction_row_index": 7, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "Capan-1", "model_type": null, "experiment_type": null, "endpoint_name": "ANTIGEN_EXPRESSION", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "In Vitro Studies", "model_type": "Cell Line Model", "citations": ["the antitumor activity of DS-8201a was evaluated in various mice xenograft models with different HER2 expression levels; KPL-4 (strong positive), JIMT-1 (moderate positive), Capan-1 (weak positive), and GCIY (negative) (Fig. 4A and B). ... A stronger efficacy was apparently observed for DS-8201a than anti-HER2 ADC (DAR 3.4) in the HER2 weak–positive Capan-1 model. These results suggest that the high DAR ADC, DS-8201a, enables the delivery of sufficient payload amounts into cancer cells, indicating cytotoxicity even with low HER2 levels."], "measured_value": "Low Antigen Expression", "measured_time": null, "measured_concentration": null}]}, "llm_response": {"matched": true, "gt_id": "GT_ROW_7", "matched_gt_endpoint": {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "Capan-1", "model_type": "Cell Line", "experiment_type": "In Vitro", "endpoint_name": "Antigen Expression (Biomarker High or Low Model)", "endpoint_type": "Biomarker", "endpoint_value": "Low Antigen Expression", "endpoint_units": "NR", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_7"}, "confidence": 1.0, "reason": "The extracted endpoint matches GT_ROW_7 which has the same ADC name (DS-8201a) and model name (Capan-1), with matching experiment type (In Vitro) and a corresponding antigen expression endpoint with value 'Low Antigen Expression'.", "reasoning": "Fallback model used due to: LLM response parsing failed: 'Completions' object has no attribute 'parse'. Step 1: The ADC and model names match (DS-8201a and Capan-1). Step 2: The extracted measurement is from an in vitro cell line study (Cell Line Model/In Vitro Studies) which corresponds with the ground truth's 'Cell Line' and 'In Vitro'. Step 3: The endpoint name (ANTIGEN_EXPRESSION compared to 'Antigen Expression (Biomarker High or Low Model)') and the value ('Low Antigen Expression') match exactly. Thus, GT_ROW_7 is the best match.", "reasoning_tokens": 448, "language_model": "o3-mini"}, "classification": "TP", "extraction_row_index": 8, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "CFPAC-1", "model_type": null, "experiment_type": null, "endpoint_name": "ANTIGEN_EXPRESSION", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "In Vitro Studies", "model_type": "Cell Line Model", "citations": ["To confirm HER2-specificity of DS-8201a in a HER2 low–expressing model, a competitive inhibition study was performed in a HER2 low CFPAC-1 model (Fig. 4C). The efficacy of DS-8201a was cancelled by the prior treatment of the anti-HER2 Ab, and the control IgG-ADC did not inhibit tumor growth at a 3-fold higher dose than DS-8201a. From these results, the HER2 specificity of DS-8201a in a HER2 low–expressing model was confirmed."], "measured_value": "Low Antigen Expression", "measured_time": null, "measured_concentration": null}]}, "llm_response": {"matched": true, "gt_id": "GT_ROW_8", "matched_gt_endpoint": {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "CFPAC-1", "model_type": "Cell Line", "experiment_type": "In Vitro", "endpoint_name": "Antigen Expression (Biomarker High or Low Model)", "endpoint_type": "Biomarker", "endpoint_value": "Low Antigen Expression", "endpoint_units": "NR", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_8"}, "confidence": 1.0, "reason": "ADC and model name match, and the extracted experimental parameters from the measurement (In Vitro Studies, Cell Line Model) align with the ground truth endpoint in GT_ROW_8. The endpoint name (antigen expression) and measured value (Low Antigen Expression) are an exact match.", "reasoning": "Fallback model used due to: LLM response parsing failed: 'Completions' object has no attribute 'parse'. Step 1: The ADC name 'DS-8201a' and model name 'CFPAC-1' match between the extracted endpoint and GT_ROW_8. Step 2: The experiment type and model type from the measurement (In Vitro Studies / Cell Line Model) correspond to 'In Vitro' and 'Cell Line' in GT_ROW_8. Step 3: The endpoint name and value ('ANTIGEN_EXPRESSION' and 'Low Antigen Expression') match, with the ground truth using a more descriptive label but representing the same measurement.", "reasoning_tokens": 512, "language_model": "o3-mini"}, "classification": "TP", "extraction_row_index": 9, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "JIMT-1", "model_type": null, "experiment_type": null, "endpoint_name": "ANTIGEN_EXPRESSION", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "In Vitro Studies", "model_type": "Cell Line Model", "citations": ["the antitumor activity of DS-8201a was evaluated in various mice xenograft models with different HER2 expression levels; KPL-4 (strong positive), JIMT-1 (moderate positive), Capan-1 (weak positive), and GCIY (negative) (Fig. 4A and B)."], "measured_value": "Moderate Antigen Expression", "measured_time": null, "measured_concentration": null}]}, "llm_response": {"matched": true, "gt_id": "GT_ROW_10", "matched_gt_endpoint": {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "JIMT-1", "model_type": "Cell Line", "experiment_type": "In Vitro", "endpoint_name": "Antigen Expression (Biomarker High or Low Model)", "endpoint_type": "Biomarker", "endpoint_value": "Moderate Antigen Expression", "endpoint_units": "NR", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_10"}, "confidence": 1.0, "reason": "The ADC name (DS-8201a) and model name (JIMT-1) in the extracted endpoint match the ground truth. The extracted model type ('Cell Line Model') and experiment type ('In Vitro Studies') align closely with the ground truth's 'Cell Line' and 'In Vitro'. The endpoint name indicates antigen expression, and the measured value 'Moderate Antigen Expression' exactly matches that in GT_ROW_10.", "reasoning": "Fallback model used due to: LLM response parsing failed: 'Completions' object has no attribute 'parse'. Step 1: The ADC name and model name match exactly (DS-8201a and JIMT-1).\n\nStep 2: The model type and experiment type are consistent between the extraction and ground truth (Cell Line Model ≈ Cell Line, In Vitro Studies ≈ In Vitro).\n\nStep 3: The endpoint name, although worded slightly differently, corresponds to antigen expression, and the measured value 'Moderate Antigen Expression' is identical to that in GT_ROW_10.\n\nThus, GT_ROW_10 is the best match.", "reasoning_tokens": 512, "language_model": "o3-mini"}, "classification": "TP", "extraction_row_index": 10, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "KPL-4", "model_type": null, "experiment_type": null, "endpoint_name": "ANTIGEN_EXPRESSION", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "In Vitro Studies", "model_type": "Cell Line Model", "citations": ["HER2 expression on the cell surface of the cell lines KPL-4, NCI-N87, SK-BR-3, and MDA-MB-468 was firstly evaluated by flow cytometric analysis (Fig. 2A). The relative MFIs of KPL-4, NCI-N87, and SK-BR-3 were 95.7, 101.6, and 56.2, respectively, suggesting that HER2 is clearly expressed on the cell surfaces, whereas the relative MFI was 1.0 for MDA-MB-468, indicating no expression in the MDA-MB-468."], "measured_value": "High Antigen Expression", "measured_time": null, "measured_concentration": null}]}, "llm_response": {"matched": true, "gt_id": "GT_ROW_11", "matched_gt_endpoint": {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "KPL-4", "model_type": "Cell Line", "experiment_type": "In Vitro", "endpoint_name": "Antigen Expression (Biomarker High or Low Model)", "endpoint_type": "Biomarker", "endpoint_value": "High Antigen Expression", "endpoint_units": "NR", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_11"}, "confidence": 1.0, "reason": "Core entities match: ADC 'DS-8201a' and model 'KPL-4'. The measurement context is in vitro cell line with endpoint indicating antigen expression. The extracted endpoint value 'High Antigen Expression' exactly matches GT_ROW_11's value. The endpoint name is a synonym variation of 'Antigen Expression (Biomarker High or Low Model)'.", "reasoning": "Fallback model used due to: LLM response parsing failed: 'Completions' object has no attribute 'parse'. Step 1: The extracted endpoint and GT_ROW_11 both have ADC 'DS-8201a' and model 'KPL-4'. Step 2: Both are in vitro studies with cell line models. Step 3: The endpoint is for antigen expression. The extracted measured value 'High Antigen Expression' exactly matches GT_ROW_11. Therefore, GT_ROW_11 is the best match.", "reasoning_tokens": 576, "language_model": "o3-mini"}, "classification": "TP", "extraction_row_index": 11, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "KPL-4", "model_type": null, "experiment_type": null, "endpoint_name": "ADC_IC50", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "In Vitro Studies", "model_type": "Cell Line Model", "citations": ["Remarkable inhibitory activity to the cell growth was observed for DS-8201a against HER2-positive KPL-4, NCI-N87, and SK-BR-3, with the IC50 values of 26.8, 25.4, and 6.7 ng/mL, respectively, whereas no such inhibition was seen against MDA-MB-468 with the IC50 value of >10,000 ng/mL (Fig. 2B)."], "measured_value": "26.8 ng/mL", "measured_time": "6 days", "measured_concentration": null}]}, "llm_response": {"matched": true, "gt_id": "GT_ROW_3", "matched_gt_endpoint": {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "KPL-4", "model_type": "Cell Line", "experiment_type": "In Vitro", "endpoint_name": "ADC IC??", "endpoint_type": "Efficacy", "endpoint_value": "26.8", "endpoint_units": "ng/mL", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_3"}, "confidence": 1.0, "reason": "Core entities (ADC name and model name) match. The in vitro experiment type and cell line model details (Cell Line Model vs Cell Line) align, and the endpoint measured value of 26.8 ng/mL with an endpoint name indicative of an IC50 measurement (", "reasoning": "Fallback model used due to: LLM response parsing failed: 'Completions' object has no attribute 'parse'. The extracted endpoint from the paper indicates an IC50 measurement for DS-8201a in KPL-4 cells with a measured value of 26.8 ng/mL and an in vitro context. GT_ROW_3 exactly matches these core details: the same ADC (DS-8201a), cell line model (KPL-4 with 'Cell Line' type matching the extracted 'Cell Line Model'), and the in vitro experiment type. Additionally, the endpoint name in GT_ROW_3 ('ADC IC??') aligns with the extracted 'ADC_IC50', and the numeric value and units are an exact match. Minor discrepancies such as the extraction's reported timepoint (6 days) versus 'NR' in the ground truth are acceptable since the primary focus is on the endpoint value and measurement type.", "reasoning_tokens": 704, "language_model": "o3-mini"}, "classification": "TP", "extraction_row_index": 12, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "NIBIO G016", "model_type": null, "experiment_type": null, "endpoint_name": "ANTIGEN_EXPRESSION", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "Ex Vivo Studies", "model_type": "Tissue Specimens", "citations": ["In a gastric cancer PDX model, NIBIO G016, DS-8201a demonstrated potent antitumor activity with tumor regression, but T-DM1 did not (Fig. 5A). As the HER2 status in this model was IHC 3+/FISH+, it is supposed that this difference in antitumor efficacy between DS-8201a and T-DM1 is based on the different sensitivity of payload due to dissimilar mechanism of action of each payload."], "measured_value": "High Antigen Expression", "measured_time": null, "measured_concentration": null}]}, "llm_response": {"matched": true, "gt_id": "GT_ROW_14", "matched_gt_endpoint": {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "NIBIO G016", "model_type": "Tissue specimens", "experiment_type": "Ex Vivo", "endpoint_name": "Antigen Expression (Biomarker High or Low Model)", "endpoint_type": "Biomarker", "endpoint_value": "High Antigen Expression", "endpoint_units": "NR", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_14"}, "confidence": 1.0, "reason": "The extracted endpoint (DS-8201a in NIBIO G016) with high antigen expression in an ex vivo tissue specimens study precisely matches GT_ROW_14.", "reasoning": "Fallback model used due to: LLM response parsing failed: 'Completions' object has no attribute 'parse'. Step 1: Both extracted and GT_ROW_14 have ADC 'DS-8201a' and model name 'NIBIO G016'. Step 2: The measurement indicates an ex vivo study using tissue specimens; GT_ROW_14 has model_type 'Tissue specimens' and experiment_type 'Ex Vivo', which matches despite slight wording differences. Step 3: The endpoint name and value ('High Antigen Expression') align with GT_ROW_14 ('Antigen Expression (Biomarker High or Low Model)' with value 'High Antigen Expression'). Thus, GT_ROW_14 is the best match.", "reasoning_tokens": 576, "language_model": "o3-mini"}, "classification": "TP", "extraction_row_index": 13, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "ST225", "model_type": null, "experiment_type": null, "endpoint_name": "TUMOR_GROWTH_INHIBITION", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "In Vivo Studies", "model_type": "Patient-Derived Xenograft (PDX)", "citations": ["In breast cancer PDX models, although both DS-8201a and T-DM1 were effective in the HER2 IHC 2+/FISH–positive ST225 model, complete tumor regression was observed on day 21 in 3 of 5 mice treated with DS-8201a, not T-DM1 (Fig. 5B)."], "measured_value": "complete tumor regression in 3 of 5 mice", "measured_time": "day 21", "measured_concentration": null}]}, "llm_response": {"matched": true, "gt_id": "GT_ROW_43", "matched_gt_endpoint": {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "ST225", "model_type": "Patient-Derived Xenograft (PDX)", "experiment_type": "In Vivo", "endpoint_name": "Tumor growth inhibition", "endpoint_type": "Efficacy", "endpoint_value": "complete tumor regression", "endpoint_units": "NR", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_43"}, "confidence": 1.0, "reason": "The extracted endpoint specifies DS-8201a, model ST225 (PDX/in vivo) with a tumor growth inhibition measurement indicating complete tumor regression, which exactly matches GT_ROW_43. The ADC name, model name, model type, experiment type, endpoint name, and the measured endpoint value all align.", "reasoning": "Fallback model used due to: LLM response parsing failed: 'Completions' object has no attribute 'parse'. Step 1: The ADC name 'DS-8201a' and model name 'ST225' from the extraction match exactly with GT_ROW_43. Step 2: The extracted experiment type 'In Vivo Studies' (converted to 'In Vivo') and model type 'Patient-Derived Xenograft (PDX)' match GT_ROW_43. Step 3: The endpoint name 'TUMOR_GROWTH_INHIBITION' and the measured outcome 'complete tumor regression' correspond well with GT_ROW_43's endpoint name 'Tumor growth inhibition' and endpoint value 'complete tumor regression'. Thus, GT_ROW_43 is the best match.", "reasoning_tokens": 512, "language_model": "o3-mini"}, "classification": "TP", "extraction_row_index": 14, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "ST565", "model_type": null, "experiment_type": null, "endpoint_name": "ANTIGEN_EXPRESSION", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "Ex Vivo Studies", "model_type": "Tissue Specimens", "citations": ["Furthermore, DS-8201a showed an antitumor activity in HER2 low–expressing ST565 and ST313 models with HER2 IHC 1+/FISH–negative expression (Fig. 5C and D), but T-DM1 did not."], "measured_value": "Low Antigen Expression", "measured_time": null, "measured_concentration": null}]}, "llm_response": {"matched": true, "gt_id": "GT_ROW_17", "matched_gt_endpoint": {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "ST565", "model_type": "Tissue specimens", "experiment_type": "Ex Vivo", "endpoint_name": "Antigen Expression (Biomarker High or Low Model)", "endpoint_type": "Biomarker", "endpoint_value": "Low Antigen Expression", "endpoint_units": "NR", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_17"}, "confidence": 1.0, "reason": "The extracted endpoint matches GT_ROW_17 based on ADC name, model name, model type (Tissue specimens vs Tissue Specimens), experiment type (Ex Vivo Studies vs Ex Vivo), and endpoint name/value corresponding to antigen expression.", "reasoning": "Fallback model used due to: LLM response parsing failed: 'Completions' object has no attribute 'parse'. Step 1: ADC name (DS-8201a) and model name (ST565) exactly match GT_ROW_17. Step 2: The experimental context is ex vivo using tissue specimens, which matches the GT_ROW_17 specification. Step 3: The endpoint name and value ('Antigen Expression' with 'Low Antigen Expression') accurately match despite minor naming differences. Therefore, GT_ROW_17 is the best match.", "reasoning_tokens": 512, "language_model": "o3-mini"}, "classification": "TP", "extraction_row_index": 15, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "ST313", "model_type": null, "experiment_type": null, "endpoint_name": "ANTIGEN_EXPRESSION", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "Ex Vivo Studies", "model_type": "Tissue Specimens", "citations": ["Furthermore, DS-8201a showed an antitumor activity in HER2 low–expressing ST565 and ST313 models with HER2 IHC 1+/FISH–negative expression (Fig. 5C and D), but T-DM1 did not."], "measured_value": "Low Antigen Expression", "measured_time": null, "measured_concentration": null}]}, "llm_response": {"matched": true, "gt_id": "GT_ROW_16", "matched_gt_endpoint": {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "ST313", "model_type": "Tissue specimens", "experiment_type": "Ex Vivo", "endpoint_name": "Antigen Expression (Biomarker High or Low Model)", "endpoint_type": "Biomarker", "endpoint_value": "Low Antigen Expression", "endpoint_units": "NR", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_16"}, "confidence": 1.0, "reason": "The ADC name (DS-8201a) and model name (ST313) match exactly. The extracted measurement's experiment type ('Ex Vivo Studies') and model type ('Tissue Specimens') align with the ground truth values ('Ex Vivo' and 'Tissue specimens'). The endpoint name and the measured value ('Low Antigen Expression') are consistent between the extraction and the ground truth.", "reasoning": "Fallback model used due to: LLM response parsing failed: 'Completions' object has no attribute 'parse'. Step 1: Core entity match confirmed: ADC name is DS-8201a and model name is ST313 in both extraction and ground truth. Step 2: The experiment type and model type are validated: the extraction indicates an ex vivo study with tissue specimens, which is aligned with the ground truth's Ex Vivo and Tissue specimens. Step 3: The endpoint name and value are consistent; 'ANTIGEN_EXPRESSION' is equivalent to 'Antigen Expression (Biomarker High or Low Model)' and the value 'Low Antigen Expression' matches exactly. Therefore, GT_ROW_16 is the best matching endpoint.", "reasoning_tokens": 768, "language_model": "o3-mini"}, "classification": "TP", "extraction_row_index": 16, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "Crl:CD(SD) rats", "model_type": null, "experiment_type": null, "endpoint_name": "TOXICOLOGY_DOSE", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "In Vivo Studies", "model_type": "Rodent Models", "citations": ["A repeated intravenous dosing (every 3 weeks for 3 doses) study was conducted in cynomolgus monkeys, the cross-reactive species for DS-8201a, and in rats (antigen–non-binding species; Table 1).", "Table 1. Summary of repeated dose toxicity studies in rats and monkeys | Species | Crl:CD(SD) rats | ... | Doses | 0, 20, 60 and 197 mg/kg | ... | Regimens | Intravenous, every 3 weeks | ... | Days 1, 22, 43 (3 times in total) | ... | Lethal dose | >197 mg/kg | ... | STD10/HNSTD | STD10: >197 mg/kg | ... | In the rat study, no deaths or life-threatening toxicities were found at dose levels up to 197 mg/kg, the maximum dose. Therefore, the severely toxic dose of 10% in animals (STD10) was considered to be >197 mg/kg."], "measured_value": ">197 mg/kg", "measured_time": null, "measured_concentration": null}]}, "llm_response": {"matched": true, "gt_id": "GT_ROW_36", "matched_gt_endpoint": {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "Rats", "model_type": "Rodent models", "experiment_type": "In Vivo", "endpoint_name": "<PERSON><PERSON>", "endpoint_type": "Safety", "endpoint_value": ">197", "endpoint_units": "mg/kg", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_36"}, "confidence": 1.0, "reason": "The extracted endpoint has ADC name DS-8201a and a rat model (Crl:CD(SD) rats) with in vivo rodent study, matching the ground truth endpoint GT_ROW_36. The measured value '>197 mg/kg' exactly matches the ground truth value for the 'Lethal Dose' endpoint.", "reasoning": "Fallback model used due to: LLM response parsing failed: 'Completions' object has no attribute 'parse'. Step 1: ADC name DS-8201a matches. Model name 'Crl:CD(SD) rats' corresponds to generic 'Rats'. Step 2: Experiment type from extraction 'In Vivo Studies' and model type 'Rodent Models' match ground truth 'In Vivo' and 'Rodent models'. Step 3: The extracted endpoint 'TOXICOLOGY_DOSE' is conceptually comparable to 'Lethal Dose' in safety toxicology with a measured value '>197 mg/kg', which is an exact match. Hence, the best matching ground truth endpoint is GT_ROW_36.", "reasoning_tokens": 896, "language_model": "o3-mini"}, "classification": "TP", "extraction_row_index": 17, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "Crl:CD(SD) rats", "model_type": null, "experiment_type": null, "endpoint_name": "LETHAL_DOSE", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "In Vivo Studies", "model_type": "Rodent Models", "citations": ["A repeated intravenous dosing (every 3 weeks for 3 doses) study was conducted in cynomolgus monkeys, the cross-reactive species for DS-8201a, and in rats (antigen–non-binding species; Table 1).", "| Species             | Crl:CD(SD) rats     | Cynomolgus monkeys     |", "| Doses               | 0, 20, 60 and 197 mg/kg | 0, 10, 30 and 78.8 mg/kg   |", "| Lethal dose         | >197 mg/kg          | 78.8 mg/kg (1 female died) |", "In the rat study, no deaths or life-threatening toxicities were found at dose levels up to 197 mg/kg, the maximum dose. Therefore, the severely toxic dose of 10% in animals (STD10) was considered to be >197 mg/kg."], "measured_value": ">197 mg/kg", "measured_time": null, "measured_concentration": null}]}, "llm_response": {"matched": false, "gt_id": null, "matched_gt_endpoint": null, "confidence": 0.0, "reason": "Matching error: LLM response parsing failed: Both primary and fallback models failed to parse response", "reasoning": null, "reasoning_tokens": null, "language_model": null}, "classification": "FP", "extraction_row_index": 18, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "Crl:CD(SD) rats", "model_type": null, "experiment_type": null, "endpoint_name": "DECREASED_FOOD_CONSUMPTION", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "In Vivo Studies", "model_type": "Rodent Models", "citations": ["DS-8201a was intravenously administered intermittently at 3-week intervals over a 6-week period to Crl:CD(SD) rats or cynomolgus monkeys (see Table 1). Clinical signs, body weight, food consumption, and clinical pathology were monitored throughout the study. A necropsy was conducted on the day after the last administration. The reversibility of the toxic changes was assessed in a subsequent 9-week recovery period in rats and in a subsequent 6-week recovery period in cynomolgus monkeys.", "| Species             | Crl:CD(SD) rats     | Cynomolgus monkeys     |", "In the rat study, no deaths or life-threatening toxicities were found at dose levels up to 197 mg/kg, the maximum dose. Therefore, the severely toxic dose of 10% in animals (STD10) was considered to be >197 mg/kg."], "measured_value": "No significant decrease in food consumption reported up to 197 mg/kg", "measured_time": null, "measured_concentration": null}]}, "llm_response": {"matched": false, "gt_id": null, "matched_gt_endpoint": null, "confidence": 1.0, "reason": "Model name and endpoint name did not match any ground truth endpoint.", "reasoning": "Fallback model used due to: LLM response parsing failed: 'Completions' object has no attribute 'parse'. The extracted endpoint is for 'DS-8201a' in 'Crl:CD(SD) rats' and measures 'DECREASED_FOOD_CONSUMPTION'. Although several ground truth endpoints have the same ADC name and are in vivo studies, none have a model name corresponding to rats with a matching endpoint name for decreased food consumption. For instance, the only similar endpoint with decreased food consumption is in Cynomolgus Monkeys (GT_ROW_33), which does not match the rat model. Therefore, strict hierarchical matching criteria are not met.", "reasoning_tokens": 1088, "language_model": "o3-mini"}, "classification": "FP", "extraction_row_index": 19, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "Crl:CD(SD) rats", "model_type": null, "experiment_type": null, "endpoint_name": "GI_ISSUES", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "In Vivo Studies", "model_type": "Rodent Models", "citations": ["In the rat study, no deaths or life-threatening toxicities were found at dose levels up to 197 mg/kg, the maximum dose. Therefore, the severely toxic dose of 10% in animals (STD10) was considered to be >197 mg/kg.", "| Species             | Crl:CD(SD) rats     | ... | Doses               | 0, 20, 60 and 197 mg/kg | ... | Target organs and tissues | 20 mg/kg: intestines, testis | 60 mg/kg: bone marrow, thymus, lymph node, skin, kidney, incisor | 78.8 mg/kg: bone marrow, kidney    |"], "measured_value": "Target organs and tissues affected at 20 mg/kg: intestines, testis; at 60 mg/kg: bone marrow, thymus, lymph node, skin, kidney, incisor; no deaths or life-threatening toxicities up to 197 mg/kg", "measured_time": null, "measured_concentration": null}]}, "llm_response": {"matched": false, "gt_id": null, "matched_gt_endpoint": null, "confidence": 1.0, "reason": "No ground truth endpoint has a matching endpoint name/component for GI_ISSUES in a rat model.", "reasoning": "Fallback model used due to: LLM response parsing failed: 'Completions' object has no attribute 'parse'. Step 1: The ADC name DS-8201a matches between the extracted endpoint and the ground truth endpoints. However, the model name in the extracted endpoint is 'Crl:CD(SD) rats' which might be loosely comparable to 'Rats' (as in GT_ROW_40), but that alone is not sufficient. Step 2: The extraction’s experiment type (In Vivo Studies) and model type (Rodent Models) roughly correspond to the GT_ROW_40 (model_type 'Rodent models', experiment_type 'In Vivo'), yet Step 3: the endpoint name in extraction is 'GI_ISSUES' with a toxicity description (target organs affected including intestines) which does not match the endpoint name 'Toxicity Dosing Regimen' in GT_ROW_40. None of the other ground truth endpoints share a matching endpoint name or detailed measurement (GI issues) for a rat/rodent model. Therefore, there is no good match.", "reasoning_tokens": 1152, "language_model": "o3-mini"}, "classification": "FP", "extraction_row_index": 20, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "Crl:CD(SD) rats", "model_type": null, "experiment_type": null, "endpoint_name": "DECREASED_BODY_WEIGHT", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "In Vivo Studies", "model_type": "Rodent Models", "citations": ["In the rat study, no deaths or life-threatening toxicities were found at dose levels up to 197 mg/kg, the maximum dose. Therefore, the severely toxic dose of 10% in animals (STD10) was considered to be >197 mg/kg. ... Body weight: 60 mg/kg: normal ... 197 mg/kg: low body weight gain", "| Species             | Crl:CD(SD) rats     | ... | Body weight         | 60 mg/kg: normal    | ... | 197 mg/kg: low body weight gain |"], "measured_value": "197 mg/kg: low body weight gain", "measured_time": null, "measured_concentration": null}]}, "llm_response": {"matched": false, "gt_id": null, "matched_gt_endpoint": null, "confidence": 1.0, "reason": "No matching ground truth endpoint found.", "reasoning": "Fallback model used due to: LLM response parsing failed: 'Completions' object has no attribute 'parse'. The extracted endpoint has ADC name DS-8201a and model name 'Crl:CD(SD) rats' with an in vivo rodent model, indicating a decreased body weight endpoint. None of the ground truth endpoints describes a decreased body weight measurement in a rodent model. The only ground truth endpoint with a rat model (GT_ROW_40: model_name 'Rat<PERSON>') has an endpoint name 'Toxicity Dosing Regimen', which does not match the extracted endpoint 'DECREASED_BODY_WEIGHT'. Therefore, the core experimental entities and endpoint names do not align, leading to no match.", "reasoning_tokens": 1024, "language_model": "o3-mini"}, "classification": "FP", "extraction_row_index": 21, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "Crl:CD(SD) rats", "model_type": null, "experiment_type": null, "endpoint_name": "REDUCED_RED_BLOOD_COUNT", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "In Vivo Studies", "model_type": "Rodent Models", "citations": ["In the rat study, no deaths or life-threatening toxicities were found at dose levels up to 197 mg/kg, the maximum dose. ... Hematology: 20 mg/kg: normal ... 60 mg/kg: decreased RBC and WBC parameters ...", "| Species             | Crl:CD(SD) rats     | ... | Doses               | 0, 20, 60 and 197 mg/kg | ... | Hematology          | 20 mg/kg: normal    | ... | 60 mg/kg: decreased RBC and WBC parameters | ..."], "measured_value": "decreased RBC parameters", "measured_time": null, "measured_concentration": null}]}, "llm_response": {"matched": false, "gt_id": null, "matched_gt_endpoint": null, "confidence": 1.0, "reason": "No matching ground truth endpoint found that has the same ADC, model, experiment type, and endpoint name. Although the extracted ADC (DS-8201a) is present in the GT list, the extracted model (Crl:CD(SD) rats) and endpoint (REDUCED_RED_BLOOD_COUNT with decreased RBC parameters) do not match the details of any GT endpoint.", "reasoning": "Fallback model used due to: LLM response parsing failed: 'Completions' object has no attribute 'parse'. Step 1: The extracted ADC name 'DS-8201a' matches the GT endpoints. Step 2: The extracted model is 'Crl:CD(SD) rats' with an In Vivo study (Rodent Models) but none of the GT endpoints with rodent models (GT_ROW_40 used 'Rats' with endpoint 'Toxicity Dosing Regimen') match the extracted endpoint details. Step 3: The endpoint name 'REDUCED_RED_BLOOD_COUNT' and measured value 'decreased RBC parameters' do not align with any GT endpoint measurement which are mostly related to dosing regimen, anti-tumor activity, pharmacodynamics, or safety endpoints in other species. Thus, no GT endpoint meets all hierarchical criteria.", "reasoning_tokens": 640, "language_model": "o3-mini"}, "classification": "FP", "extraction_row_index": 22, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "Crl:CD(SD) rats", "model_type": null, "experiment_type": null, "endpoint_name": "WHITE_BLOOD_CELLS", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "In Vivo Studies", "model_type": "Rodent Models", "citations": ["In the rat study, no deaths or life-threatening toxicities were found at dose levels up to 197 mg/kg, the maximum dose. ... Hematology: 20 mg/kg: normal ... 60 mg/kg: decreased RBC and WBC parameters ... (Table 1).", "| Species             | Crl:CD(SD) rats     | ... | Hematology          | 20 mg/kg: normal    | ... | 60 mg/kg: decreased RBC and WBC parameters |"], "measured_value": "20 mg/kg: normal; 60 mg/kg: decreased WBC parameters", "measured_time": null, "measured_concentration": null}]}, "llm_response": {"matched": false, "gt_id": null, "matched_gt_endpoint": null, "confidence": 1.0, "reason": "No ground truth endpoint matches the extracted endpoint with 'WHITE_BLOOD_CELLS' measurement in Crl:CD(SD) rats. Although the ADC name DS-8201a matches, none of the ground truth endpoints specify a white blood cell measurement or hematology endpoint in a rat model.", "reasoning": "Fallback model used due to: LLM response parsing failed: 'Completions' object has no attribute 'parse'. Step 1: The extracted ADC name DS-8201a matches the ground truth endpoints. Step 2: However, the extracted model 'Crl:CD(SD) rats' (a rodent model) does not closely match any ground truth endpoint model because the only rodent endpoint provided is in GT_ROW_40 with model_name '<PERSON><PERSON>', but its endpoint_name is 'Toxicity Dosing Regimen', not related to white blood cell counts. Step 3: The endpoint 'WHITE_BLOOD_CELLS' does not match any of the endpoint names in the provided ground truth list (which include ADC Kd, ADC EC??, Anti-tumor activity Dose, etc.). Thus, no good match exists, resulting in a mismatch.", "reasoning_tokens": 448, "language_model": "o3-mini"}, "classification": "FP", "extraction_row_index": 23, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "Crl:CD(SD) rats", "model_type": null, "experiment_type": null, "endpoint_name": "TARGET_ORGAN_ISSUES", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "In Vivo Studies", "model_type": "Rodent Models", "citations": ["A repeated intravenous dosing (every 3 weeks for 3 doses) study was conducted in cynomolgus monkeys, the cross-reactive species for DS-8201a, and in rats (antigen–non-binding species; Table 1).", "Table 1. Summary of repeated dose toxicity studies in rats and monkeys | Species | Crl:CD(SD) rats | ... | Doses | 0, 20, 60 and 197 mg/kg | ... | Target organs and tissues | 20 mg/kg: intestines, testis | 60 mg/kg: bone marrow, thymus, lymph node, skin, kidney, incisor | 78.8 mg/kg: bone marrow, kidney | ... | STD10: >197 mg/kg", "In the rat study, no deaths or life-threatening toxicities were found at dose levels up to 197 mg/kg, the maximum dose. Therefore, the severely toxic dose of 10% in animals (STD10) was considered to be >197 mg/kg."], "measured_value": "Target organ toxicities observed included intestines and testis at 20 mg/kg; bone marrow, thymus, lymph node, skin, kidney, and incisor at 60 mg/kg; bone marrow and kidney at 197 mg/kg. No deaths or life-threatening toxicities up to 197 mg/kg. STD10 >197 mg/kg.", "measured_time": null, "measured_concentration": null}]}, "llm_response": {"matched": false, "gt_id": null, "matched_gt_endpoint": null, "confidence": 0.0, "reason": "Matching error: LLM response parsing failed: Both primary and fallback models failed to parse response", "reasoning": null, "reasoning_tokens": null, "language_model": null}, "classification": "FP", "extraction_row_index": 24, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "Cynomolgus monkeys", "model_type": null, "experiment_type": null, "endpoint_name": "PK_DOSE", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "In Vivo Studies", "model_type": "Non-Human Primate Models", "citations": ["Pharmacokinetics in cynomolgus monkeys: The plasma DS-8201a concentrations decreased exponentially after a single intravenous administration of DS-8201a. ... DS-8201a was intravenously administered at 3.0 mg/kg to male cynomolgus monkeys. Plasma concentrations of DS-8201a, total antibody, and DXd were measured up to 672 hours postdose."], "measured_value": "3.0 mg/kg, single intravenous administration", "measured_time": null, "measured_concentration": null}]}, "llm_response": {"matched": false, "gt_id": null, "matched_gt_endpoint": null, "confidence": 1.0, "reason": "No ground truth endpoint with endpoint name 'PK_DOSE' exists for DS-8201a in Cynomolgus Monkeys.", "reasoning": "Fallback model used due to: LLM response parsing failed: 'Completions' object has no attribute 'parse'. Step 1: The ADC name 'DS-8201a' and the model 'Cynomolgus monkeys' match with the ground truth entries for Cynomolgus Monkeys (e.g., GT_ROW_33 to GT_ROW_39). Step 2: The extracted measurement indicates an in vivo study with non-human primate models, aligning with the ground truth’s model type 'Non-human primates' and experiment type 'In Vivo'. Step 3: However, the extracted endpoint name is 'PK_DOSE' with a value '3.0 mg/kg, single intravenous administration', which does not match any of the ground truth endpoints, all of which are different names related to safety endpoints (e.g., 'Decreased Food Consumption', 'HNSTD', 'Lethal Dose', etc.). As a result of this endpoint name/value mismatch, no appropriate match was found.", "reasoning_tokens": 832, "language_model": "o3-mini"}, "classification": "FP", "extraction_row_index": 25, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "Cynomolgus monkeys", "model_type": null, "experiment_type": null, "endpoint_name": "TOXICOLOGY_DOSE", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "In Vivo Studies", "model_type": "Non-Human Primate Models", "citations": ["A repeated intravenous dosing (every 3 weeks for 3 doses) study was conducted in cynomolgus monkeys, the cross-reactive species for DS-8201a, and in rats (antigen–non-binding species; Table 1).", "| Species             | Crl:CD(SD) rats     | Cynomolgus monkeys     |", "| Doses               | 0, 20, 60 and 197 mg/kg | 0, 10, 30 and 78.8 mg/kg   |", "| Regimens            | Intravenous, every 3 weeks | Intravenous, every 3 weeks |", "|                     | Days 1, 22, 43 (3 times in total) | Days 1, 22, 43 (3 times in total) |", "| Lethal dose         | >197 mg/kg          | 78.8 mg/kg (1 female died) |", "| Body weight         | 60 mg/kg: normal    | 30 mg/kg: normal       |", "|                     | 197 mg/kg: low body weight gain | 78.8 mg/kg: decreased in 1 male and 1 female |", "| Hematology          | 20 mg/kg: normal    | 30 mg/kg: normal       |", "|                     | 60 mg/kg: decreased RBC and WBC parameters | 78.8 mg/kg: decreased RBC parameters |", "| Target organs and tissues | 20 mg/kg: intestines, testis | 10 mg/kg: intestines  |", "|                     | 60 mg/kg: bone marrow, thymus, lymph node, skin, kidney, incisor | 30 mg/kg: lung, skin, testis |", "|                     | 78.8 mg/kg: bone marrow, kidney    |                     |", "| STD10/HNSTD         | STD10: >197 mg/kg   | HNSTD: 30 mg/kg        |", "In the monkey study, one female at the highest dose of 78.8 mg/kg was euthanized due to moribundity on day 26. The cause of the moribundity appeared to be the deteriorated condition of the animal, which included decreased body weight and food consumption, as well as bone marrow toxicity and intestinal toxicity.", "The bone marrow toxicity was produced only at 78.8 mg/kg, and was accompanied by decreases in reticulocyte ratios. No abnormalities in leukocyte and erythrocyte counts were observed in monkeys at 10 and 30 mg/kg.", "The repeated dose of DS-8201a caused moderate pulmonary toxicity in monkeys at 78.8 mg/kg, and findings graded as slight or very slight after the 6-week recovery period at 30 mg/kg.", "On the basis of the mortality and severity of the findings above, the highest non-severely toxic dose (HNSTD) for monkeys was considered to be 30 mg/kg. DS-8201a was well tolerated at the doses up to 197 mg/kg in rats and 30 mg/kg in monkeys following the repeated administration corresponding to the clinical regimen, and the nonclinical safety profile was acceptable for entry into human trials."], "measured_value": "30 mg/kg (HNSTD, highest non-severely toxic dose)", "measured_time": null, "measured_concentration": null}]}, "llm_response": {"matched": true, "gt_id": "GT_ROW_34", "matched_gt_endpoint": {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "Cynomolgus Monkeys", "model_type": "Non-human primates", "experiment_type": "In Vivo", "endpoint_name": "HNSTD", "endpoint_type": "Safety", "endpoint_value": "30", "endpoint_units": "mg/kg", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_34"}, "confidence": 1.0, "reason": "Extracted endpoint for DS-8201a in Cynomolgus monkeys with a measured HNSTD of 30 mg/kg matches the ground truth endpoint GT_ROW_34. The ADC name, model (non-human primates), experiment type (In Vivo), and endpoint metric (HNSTD) with the value of 30 mg/kg align closely.", "reasoning": "Fallback model used due to: LLM response parsing failed: 'Completions' object has no attribute 'parse'. Step 1: The ADC name 'DS-8201a' and model 'Cynomolgus monkeys' match the ground truth endpoint's ADC and model. Step 2: The experimental details—extracted experiment (In Vivo Studies with Non-Human Primate Models) are aligned with 'In Vivo' and 'Non-human primates' in the ground truth. Step 3: The endpoint 'TOXICOLOGY_DOSE' with a measured value of '30 mg/kg (HNSTD, highest non-severely toxic dose)' corresponds clearly to the endpoint named 'HNSTD' in the ground truth, which has a value of 30 mg/kg. All hierarchical criteria match.", "reasoning_tokens": 768, "language_model": "o3-mini"}, "classification": "TP", "extraction_row_index": 26, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "Cynomolgus monkeys", "model_type": null, "experiment_type": null, "endpoint_name": "LETHAL_DOSE", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "In Vivo Studies", "model_type": "Non-Human Primate Models", "citations": ["A repeated intravenous dosing (every 3 weeks for 3 doses) study was conducted in cynomolgus monkeys, the cross-reactive species for DS-8201a, and in rats (antigen–non-binding species; Table 1).", "| Species             | Crl:CD(SD) rats     | Cynomolgus monkeys     |\n|---------------------|---------------------|------------------------|\n| Doses               | 0, 20, 60 and 197 mg/kg | 0, 10, 30 and 78.8 mg/kg   |\n| Regimens            | Intravenous, every 3 weeks | Intravenous, every 3 weeks |\n|                     | Days 1, 22, 43 (3 times in total) | Days 1, 22, 43 (3 times in total) |\n| No. of animals      | 10/sex/group (Main): all dose groups | 3/sex/group (Main): all dose groups |\n|                     | 5/sex/group (Recovery): 60 and 197 mg/kg groups | 2/sex/group (Recovery): 30 and 78.8 mg/kg groups |\n| Lethal dose         | >197 mg/kg          | 78.8 mg/kg (1 female died) |", "In the monkey study, one female at the highest dose of 78.8 mg/kg was euthanized due to moribundity on day 26. The cause of the moribundity appeared to be the deteriorated condition of the animal, which included decreased body weight and food consumption, as well as bone marrow toxicity and intestinal toxicity."], "measured_value": "78.8 mg/kg (1 female died)", "measured_time": null, "measured_concentration": null}]}, "llm_response": {"matched": true, "gt_id": "GT_ROW_35", "matched_gt_endpoint": {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "Cynomolgus Monkeys", "model_type": "Non-human primates", "experiment_type": "In Vivo", "endpoint_name": "<PERSON><PERSON>", "endpoint_type": "Safety", "endpoint_value": "78.8", "endpoint_units": "mg/kg (1 female died)", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_35"}, "confidence": 1.0, "reason": "The extracted endpoint matches the ground truth GT_ROW_35: identical ADC name (DS-8201a), matching model (Cynomolgus monkeys vs. Cynomolgus Monkeys) with valid model type (Non-human primates) and experiment type (In Vivo), and endpoint name '<PERSON><PERSON>' with corresponding value '78.8 mg/kg (1 female died)'.", "reasoning": "Fallback model used due to: LLM response parsing failed: 'Completions' object has no attribute 'parse'. Step 1: ADC name 'DS-8201a' matches and model 'Cynomolgus monkeys' is the same as in GT_ROW_35 (case insensitive match). Step 2: Experiment type 'In Vivo' and model type 'Non-human primates' in GT_ROW_35 strictly match the context provided by the extraction. Step 3: Endpoint name 'LETHAL_DOSE' aligns with 'Lethal Dose' and the measured value '78.8 mg/kg (1 female died)' matches the endpoint value and units in GT_ROW_35.", "reasoning_tokens": 320, "language_model": "o3-mini"}, "classification": "TP", "extraction_row_index": 27, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "Cynomolgus monkeys", "model_type": null, "experiment_type": null, "endpoint_name": "HNSTD", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "In Vivo Studies", "model_type": "Non-Human Primate Models", "citations": ["A repeated intravenous dosing (every 3 weeks for 3 doses) study was conducted in cynomolgus monkeys, the cross-reactive species for DS-8201a, and in rats (antigen–non-binding species; Table 1).", "| Species             | Crl:CD(SD) rats     | Cynomolgus monkeys     |", "| Doses               | 0, 20, 60 and 197 mg/kg | 0, 10, 30 and 78.8 mg/kg   |", "| Regimens            | Intravenous, every 3 weeks | Intravenous, every 3 weeks |", "|                     | Days 1, 22, 43 (3 times in total) | Days 1, 22, 43 (3 times in total) |", "| Lethal dose         | >197 mg/kg          | 78.8 mg/kg (1 female died) |", "| Body weight         | 60 mg/kg: normal    | 30 mg/kg: normal       |", "|                     | 197 mg/kg: low body weight gain | 78.8 mg/kg: decreased in 1 male and 1 female |", "| Hematology          | 20 mg/kg: normal    | 30 mg/kg: normal       |", "|                     | 60 mg/kg: decreased RBC and WBC parameters | 78.8 mg/kg: decreased RBC parameters |", "| Target organs and tissues | 20 mg/kg: intestines, testis | 10 mg/kg: intestines  |", "|                     | 60 mg/kg: bone marrow, thymus, lymph node, skin, kidney, incisor | 30 mg/kg: lung, skin, testis |", "|                     | 78.8 mg/kg: bone marrow, kidney    |                     |", "| STD10/HNSTD         | STD10: >197 mg/kg   | HNSTD: 30 mg/kg        |", "In the monkey study, one female at the highest dose of 78.8 mg/kg was euthanized due to moribundity on day 26. The cause of the moribundity appeared to be the deteriorated condition of the animal, which included decreased body weight and food consumption, as well as bone marrow toxicity and intestinal toxicity... On the basis of the mortality and severity of the findings above, the highest non-severely toxic dose (HNSTD) for monkeys was considered to be 30 mg/kg. DS-8201a was well tolerated at the doses up to 197 mg/kg in rats and 30 mg/kg in monkeys following the repeated administration corresponding to the clinical regimen, and the nonclinical safety profile was acceptable for entry into human trials."], "measured_value": "30 mg/kg", "measured_time": null, "measured_concentration": null}]}, "llm_response": {"matched": false, "gt_id": null, "matched_gt_endpoint": null, "confidence": 0.0, "reason": "Matching error: LLM response parsing failed: Both primary and fallback models failed to parse response", "reasoning": null, "reasoning_tokens": null, "language_model": null}, "classification": "FP", "extraction_row_index": 28, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "Cynomolgus monkeys", "model_type": null, "experiment_type": null, "endpoint_name": "GI_ISSUES", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "In Vivo Studies", "model_type": "Non-Human Primate Models", "citations": ["A repeated intravenous dosing (every 3 weeks for 3 doses) study was conducted in cynomolgus monkeys, the cross-reactive species for DS-8201a, and in rats (antigen–non-binding species; Table 1).", "In the monkey study, one female at the highest dose of 78.8 mg/kg was euthanized due to moribundity on day 26. The cause of the moribundity appeared to be the deteriorated condition of the animal, which included decreased body weight and food consumption, as well as bone marrow toxicity and intestinal toxicity. Microscopic findings in the intestines, bone marrow and lungs in the surviving monkeys are shown in Supplementary Table S1. Gastrointestinal toxicity and bone marrow toxicity are typical dose-limiting factors in the clinical use of topoisomerase I inhibitors. The effects of DS-8201a on the intestines were very slight, and severe changes were not pronounced in any animal at up to 78.8 mg/kg.", "The effects of DS-8201a on the intestines were very slight, and severe changes were not pronounced in any animal at up to 78.8 mg/kg."], "measured_value": "very slight gastrointestinal toxicity; severe changes were not pronounced in any animal at up to 78.8 mg/kg", "measured_time": null, "measured_concentration": null}]}, "llm_response": {"matched": false, "gt_id": null, "matched_gt_endpoint": null, "confidence": 1.0, "reason": "No matching ground truth endpoint.", "reasoning": "Fallback model used due to: LLM response parsing failed: 'Completions' object has no attribute 'parse'. Step 1: The extracted endpoint has ADC name 'DS-8201a' and model name 'Cynomolgus monkeys', which suggests using the non-human primate model. In the ground truth endpoints, the ones with Cynomolgus Monkeys are GT_ROW_33, GT_ROW_37, GT_ROW_38, and GT_ROW_39. \nStep 2: The extraction measurement comes from an in vivo study with non-human primates. The ground truth endpoints with Cynomolgus Monkeys are indeed in vivo but have endpoint names 'Decreased Food Consumption', 'Pulmonary Toxicity', or 'Toxicity Dosing Regimen'. \nStep 3: The extracted endpoint’s endpoint name is 'GI_ISSUES' and measured value indicates very slight gastrointestinal toxicity. None of the available ground truth endpoints for Cynomolgus Monkeys address gastrointestinal toxicity. They focus on food consumption, pulmonary toxicity, or dosing regimens, and do not mention gastrointestinal issues. \nDue to the mismatch in endpoint names and intended measurement, no good match exists.", "reasoning_tokens": 576, "language_model": "o3-mini"}, "classification": "FP", "extraction_row_index": 29, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "Cynomolgus monkeys", "model_type": null, "experiment_type": null, "endpoint_name": "DECREASED_BODY_WEIGHT", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "In Vivo Studies", "model_type": "Non-Human Primate Models", "citations": ["A repeated intravenous dosing (every 3 weeks for 3 doses) study was conducted in cynomolgus monkeys, the cross-reactive species for DS-8201a, and in rats (antigen–non-binding species; Table 1).", "| Species             | Crl:CD(SD) rats     | Cynomolgus monkeys     |", "| Doses               | 0, 20, 60 and 197 mg/kg | 0, 10, 30 and 78.8 mg/kg   |", "| Body weight         | 60 mg/kg: normal    | 30 mg/kg: normal       |", "|                     | 197 mg/kg: low body weight gain | 78.8 mg/kg: decreased in 1 male and 1 female |", "In the monkey study, one female at the highest dose of 78.8 mg/kg was euthanized due to moribundity on day 26. The cause of the moribundity appeared to be the deteriorated condition of the animal, which included decreased body weight and food consumption, as well as bone marrow toxicity and intestinal toxicity."], "measured_value": "decreased in 1 male and 1 female at 78.8 mg/kg; normal at 30 mg/kg", "measured_time": null, "measured_concentration": null}]}, "llm_response": {"matched": false, "gt_id": null, "matched_gt_endpoint": null, "confidence": 1.0, "reason": "No matching endpoint found.", "reasoning": "Fallback model used due to: LLM response parsing failed: 'Completions' object has no attribute 'parse'. The extracted endpoint describes a decreased body weight effect in Cynomolgus monkeys with DS-8201a in an In Vivo study. However, none of the ground truth endpoints for Cynomolgus monkeys have an endpoint name that corresponds to decreased body weight. The available ground truth endpoints for Cynomolgus monkeys pertain to decreased food consumption, pulmonary toxicity, or dosing regimen, none of which match the decreased body weight measurement. Therefore, no good match exists.", "reasoning_tokens": 832, "language_model": "o3-mini"}, "classification": "FP", "extraction_row_index": 30, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "Cynomolgus monkeys", "model_type": null, "experiment_type": null, "endpoint_name": "REDUCED_RED_BLOOD_COUNT", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "In Vivo Studies", "model_type": "Non-Human Primate Models", "citations": ["A repeated intravenous dosing (every 3 weeks for 3 doses) study was conducted in cynomolgus monkeys, the cross-reactive species for DS-8201a, and in rats (antigen–non-binding species; Table 1).", "| Species             | Crl:CD(SD) rats     | Cynomolgus monkeys     |", "| Doses               | 0, 20, 60 and 197 mg/kg | 0, 10, 30 and 78.8 mg/kg   |", "| Hematology          | 20 mg/kg: normal    | 30 mg/kg: normal       |", "|                     | 60 mg/kg: decreased RBC and WBC parameters | 78.8 mg/kg: decreased RBC parameters |", "No abnormalities in leukocyte and erythrocyte counts were observed in monkeys at 10 and 30 mg/kg. The repeated dose of DS-8201a caused moderate pulmonary toxicity in monkeys at 78.8 mg/kg, and findings graded as slight or very slight after the 6-week recovery period at 30 mg/kg."], "measured_value": "No abnormalities in erythrocyte counts at 10 and 30 mg/kg; Decreased RBC parameters at 78.8 mg/kg", "measured_time": null, "measured_concentration": null}]}, "llm_response": {"matched": false, "gt_id": null, "matched_gt_endpoint": null, "confidence": 1.0, "reason": "No match found", "reasoning": "Fallback model used due to: LLM response parsing failed: 'Completions' object has no attribute 'parse'. The extracted endpoint has the ADC DS-8201a and the model Cynomolgus monkeys with an in vivo study measuring a change in red blood cell count (REDUCED_RED_BLOOD_COUNT). However, none of the available ground truth endpoints for Cynomolgus monkeys correspond to a red blood cell measurement. The listed endpoints for Cynomolgus monkeys in the ground truth are related to safety endpoints such as decreased food consumption, pulmonary toxicity, or dosing regimen. Therefore, there is no match.", "reasoning_tokens": 768, "language_model": "o3-mini"}, "classification": "FP", "extraction_row_index": 31, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "Cynomolgus monkeys", "model_type": null, "experiment_type": null, "endpoint_name": "WHITE_BLOOD_CELLS", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "In Vivo Studies", "model_type": "Non-Human Primate Models", "citations": ["A repeated intravenous dosing (every 3 weeks for 3 doses) study was conducted in cynomolgus monkeys, the cross-reactive species for DS-8201a, and in rats (antigen–non-binding species; Table 1).", "| Species             | Crl:CD(SD) rats     | Cynomolgus monkeys     |\n|---------------------|---------------------|------------------------|\n| Doses               | 0, 20, 60 and 197 mg/kg | 0, 10, 30 and 78.8 mg/kg   |\n| Regimens            | Intravenous, every 3 weeks | Intravenous, every 3 weeks |\n|                     | Days 1, 22, 43 (3 times in total) | Days 1, 22, 43 (3 times in total) |\n| Hematology          | 20 mg/kg: normal    | 30 mg/kg: normal       |\n|                     | 60 mg/kg: decreased RBC and WBC parameters | 78.8 mg/kg: decreased RBC parameters |", "No abnormalities in leukocyte and erythrocyte counts were observed in monkeys at 10 and 30 mg/kg."], "measured_value": "normal (no abnormalities in leukocyte counts)", "measured_time": null, "measured_concentration": null}]}, "llm_response": {"matched": false, "gt_id": null, "matched_gt_endpoint": null, "confidence": 1.0, "reason": "The extracted endpoint involves a measurement of white blood cells (leukocyte counts) in Cynomolgus monkeys, while the available ground truth endpoints for Cynomolgus Monkeys pertain to safety endpoints such as decreased food consumption, pulmonary toxicity, and dosing regimen. The endpoint names do not match, leading to no valid match.", "reasoning": "Fallback model used due to: LLM response parsing failed: 'Completions' object has no attribute 'parse'. Step 1: The ADC name DS-8201a and model name (Cynomolgus monkeys) in the extraction align with the ground truth endpoints for Cynomolgus Monkeys. Step 2: The experimental type in the extraction (In Vivo Studies) is considered to match 'In Vivo' in the ground truth endpoints, and the model type (Non-Human Primate Models vs Non-human primates) is acceptable. Step 3: However, the endpoint name in the extraction ('WHITE_BLOOD_CELLS') does not have any counterpart in the ground truth endpoints (which include 'Decreased Food Consumption', 'Pulmonary Toxicity', and 'Toxicity Dosing Regimen'). Therefore, the hierarchical matching fails at the endpoint name level.", "reasoning_tokens": 960, "language_model": "o3-mini"}, "classification": "FP", "extraction_row_index": 32, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "Cynomolgus monkeys", "model_type": null, "experiment_type": null, "endpoint_name": "LUNG_INFLAMMATION", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "In Vivo Studies", "model_type": "Non-Human Primate Models", "citations": ["A repeated intravenous dosing (every 3 weeks for 3 doses) study was conducted in cynomolgus monkeys, the cross-reactive species for DS-8201a, and in rats (antigen–non-binding species; Table 1).", "The repeated dose of DS-8201a caused moderate pulmonary toxicity in monkeys at 78.8 mg/kg, and findings graded as slight or very slight after the 6-week recovery period at 30 mg/kg. On the basis of the mortality and severity of the findings above, the highest non-severely toxic dose (HNSTD) for monkeys was considered to be 30 mg/kg.", "DS-8201a caused pulmonary toxicity in monkeys at 30 mg/kg (every 3 weeks for 3 doses), while it was not observed in rats (antigen–nonbinding species)."], "measured_value": "moderate pulmonary toxicity at 78.8 mg/kg; slight or very slight pulmonary toxicity after 6-week recovery at 30 mg/kg; pulmonary toxicity at 30 mg/kg", "measured_time": null, "measured_concentration": null}]}, "llm_response": {"matched": true, "gt_id": "GT_ROW_37", "matched_gt_endpoint": {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "Cynomolgus Monkeys", "model_type": "Non-human primates", "experiment_type": "In Vivo", "endpoint_name": "Pulmonary Toxicity", "endpoint_type": "Safety", "endpoint_value": "moderate pulmonary toxicity", "endpoint_units": "NR", "endpoint_timepoint": "NR", "endpoint_concentration": "78.8 mg/kg", "gt_id": "GT_ROW_37"}, "confidence": 0.95, "reason": "The extracted endpoint has ADC DS-8201a tested in Cynomolgus monkeys, corresponding to a non-human primate in vivo study, with reported moderate pulmonary toxicity. This matches closely with the ground truth endpoint GT_ROW_37 which reports 'Pulmonary Toxicity' with a value 'moderate pulmonary toxicity' and a concentration of '78.8 mg/kg'. Although the extraction endpoint is labeled 'LUNG_INFLAMMATION', it aligns with pulmonary toxicity findings.", "reasoning": "Fallback model used due to: LLM response parsing failed: 'Completions' object has no attribute 'parse'. Step 1: Both the extraction and the ground truth have ADC DS-8201a and involve Cynomolgus monkeys. Step 2: The experimental context (non-human primates, in vivo study) also matches. Step 3: The reported measurement in the extraction ('moderate pulmonary toxicity at 78.8 mg/kg') aligns with the ground truth endpoint GT_ROW_37 which lists 'Pulmonary Toxicity' with moderate severity. Therefore, GT_ROW_37 is the best match.", "reasoning_tokens": 768, "language_model": "o3-mini"}, "classification": "TP", "extraction_row_index": 33, "comments": null, "source": null, "status": null, "status_cat": null}, {"extraction_row": {"paper_id": "w2314772071", "type": null, "adc_name": "DS-8201a", "model_name": "Cynomolgus monkeys", "model_type": null, "experiment_type": null, "endpoint_name": "TARGET_ORGAN_ISSUES", "endpoint_type": null, "comments": null, "source": null, "status": null, "status_cat": null, "endpoint_measurements": [{"experiment_type": "In Vivo Studies", "model_type": "Non-Human Primate Models", "citations": ["A repeated intravenous dosing (every 3 weeks for 3 doses) study was conducted in cynomolgus monkeys, the cross-reactive species for DS-8201a, and in rats (antigen–non-binding species; Table 1).", "In the monkey study, one female at the highest dose of 78.8 mg/kg was euthanized due to moribundity on day 26. The cause of the moribundity appeared to be the deteriorated condition of the animal, which included decreased body weight and food consumption, as well as bone marrow toxicity and intestinal toxicity. Microscopic findings in the intestines, bone marrow and lungs in the surviving monkeys are shown in Supplementary Table S1. Gastrointestinal toxicity and bone marrow toxicity are typical dose-limiting factors in the clinical use of topoisomerase I inhibitors. The effects of DS-8201a on the intestines were very slight, and severe changes were not pronounced in any animal at up to 78.8 mg/kg. The bone marrow toxicity was produced only at 78.8 mg/kg, and was accompanied by decreases in reticulocyte ratios. No abnormalities in leukocyte and erythrocyte counts were observed in monkeys at 10 and 30 mg/kg. The repeated dose of DS-8201a caused moderate pulmonary toxicity in monkeys at 78.8 mg/kg, and findings graded as slight or very slight after the 6-week recovery period at 30 mg/kg. On the basis of the mortality and severity of the findings above, the highest non-severely toxic dose (HNSTD) for monkeys was considered to be 30 mg/kg. DS-8201a was well tolerated at the doses up to 197 mg/kg in rats and 30 mg/kg in monkeys following the repeated administration corresponding to the clinical regimen, and the nonclinical safety profile was acceptable for entry into human trials.", "Table 1. Summary of repeated dose toxicity studies in rats and monkeys ... | Species | Crl:CD(SD) rats | Cynomolgus monkeys | ... | Doses | 0, 20, 60 and 197 mg/kg | 0, 10, 30 and 78.8 mg/kg | ... | Target organs and tissues | 20 mg/kg: intestines, testis | 10 mg/kg: intestines | | 60 mg/kg: bone marrow, thymus, lymph node, skin, kidney, incisor | 30 mg/kg: lung, skin, testis | | 78.8 mg/kg: bone marrow, kidney | | STD10/HNSTD | STD10: >197 mg/kg | HNSTD: 30 mg/kg |"], "measured_value": "At 10 mg/kg, intestines were affected; at 30 mg/kg, lung, skin, and testis were affected; at 78.8 mg/kg, bone marrow toxicity, intestinal toxicity, and moderate pulmonary toxicity were observed. The effects on intestines were very slight at all doses, bone marrow toxicity was only at 78.8 mg/kg, and moderate pulmonary toxicity at 78.8 mg/kg, with slight or very slight pulmonary findings after 6-week recovery at 30 mg/kg. No abnormalities in leukocyte and erythrocyte counts at 10 and 30 mg/kg.", "measured_time": null, "measured_concentration": null}]}, "llm_response": {"matched": false, "gt_id": null, "matched_gt_endpoint": null, "confidence": 1.0, "reason": "The extracted endpoint has ADC DS-8201a in Cynomolgus monkeys with a general safety/tissue toxicity measurement described as TARGET_ORGAN_ISSUES, but none of the ground truth endpoints for Cynomolgus Monkeys match this endpoint name. The available ground truth endpoints for cynomolgus monkeys focus on decreased food consumption, pulmonary toxicity, or dosing regimen, which do not capture the composite target organ issues described.", "reasoning": "Fallback model used due to: LLM response parsing failed: 'Completions' object has no attribute 'parse'. Step 1: The ADC and model name match with cynomolgus monkeys in GT_ROW_33, GT_ROW_38, and GT_ROW_39. Step 2: Although the experiment type and model type from the measurement (In Vivo Studies; Non-Human Primate Models) align with the ground truth endpoints, Step 3: the endpoint name does not match. The extracted endpoint is labeled TARGET_ORGAN_ISSUES with a complex description of multiple organ toxicities, whereas the ground truth endpoints use specific names like 'Decreased Food Consumption', 'Pulmonary Toxicity', or 'Toxicity Dosing Regimen'. Hence, none of the available ground truth endpoints are a proper match.", "reasoning_tokens": 1152, "language_model": "o3-mini"}, "classification": "FP", "extraction_row_index": 34, "comments": null, "source": null, "status": null, "status_cat": null}], "unmatched_gt_items": [{"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "SK-BR-3", "model_type": "Cell Line", "experiment_type": "In Vitro", "endpoint_name": "ADC Kd", "endpoint_type": "Pharmacodynamics (PD)", "endpoint_value": "7.3", "endpoint_units": "ng/mL", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_1", "type": "endpoint"}, {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "SK-BR-3", "model_type": "Cell Line", "experiment_type": "In Vitro", "endpoint_name": "ADC EC??", "endpoint_type": "Efficacy", "endpoint_value": "3.8", "endpoint_units": "ng/ml", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_2", "type": "endpoint"}, {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "GCIY", "model_type": "Cell Line", "experiment_type": "In Vitro", "endpoint_name": "Antigen Expression (Biomarker High or Low Model)", "endpoint_type": "Biomarker", "endpoint_value": "Low Antigen Expression", "endpoint_units": "NR", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_9", "type": "endpoint"}, {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "Capan-1", "model_type": "Cell Line-Derived Xenograft (CDX)", "experiment_type": "In Vivo", "endpoint_name": "Anti-tumor activity Dose", "endpoint_type": "Pharmacodynamics (PD)", "endpoint_value": "10", "endpoint_units": "mg/kg", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_18", "type": "endpoint"}, {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "CFPAC-1", "model_type": "Cell Line-Derived Xenograft (CDX)", "experiment_type": "In Vivo", "endpoint_name": "Anti-tumor activity Dose", "endpoint_type": "Pharmacodynamics (PD)", "endpoint_value": "1", "endpoint_units": "mg/kg", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_19", "type": "endpoint"}, {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "GCIY", "model_type": "Cell Line-Derived Xenograft (CDX)", "experiment_type": "In Vivo", "endpoint_name": "Anti-tumor activity Dose", "endpoint_type": "Pharmacodynamics (PD)", "endpoint_value": "10", "endpoint_units": "mg/kg", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_20", "type": "endpoint"}, {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "JIMT-1", "model_type": "Cell Line-Derived Xenograft (CDX)", "experiment_type": "In Vivo", "endpoint_name": "Anti-tumor activity Dose", "endpoint_type": "Pharmacodynamics (PD)", "endpoint_value": "10", "endpoint_units": "mg/kg", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_21", "type": "endpoint"}, {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "KPL-4", "model_type": "Cell Line-Derived Xenograft (CDX)", "experiment_type": "In Vivo", "endpoint_name": "Anti-tumor activity Dose", "endpoint_type": "Pharmacodynamics (PD)", "endpoint_value": "10", "endpoint_units": "mg/kg", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_22", "type": "endpoint"}, {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "NCI-N87", "model_type": "Cell Line-Derived Xenograft (CDX)", "experiment_type": "In Vivo", "endpoint_name": "Anti-tumor activity Dose", "endpoint_type": "Pharmacodynamics (PD)", "endpoint_value": "0.25", "endpoint_units": "mg/kg", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_23", "type": "endpoint"}, {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "NCI-N87", "model_type": "Cell Line-Derived Xenograft (CDX)", "experiment_type": "In Vivo", "endpoint_name": "Anti-tumor activity Dose", "endpoint_type": "Pharmacodynamics (PD)", "endpoint_value": "0.5", "endpoint_units": "mg/kg", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_24", "type": "endpoint"}, {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "NCI-N87", "model_type": "Cell Line-Derived Xenograft (CDX)", "experiment_type": "In Vivo", "endpoint_name": "Anti-tumor activity Dose", "endpoint_type": "Pharmacodynamics (PD)", "endpoint_value": "1", "endpoint_units": "mg/kg", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_25", "type": "endpoint"}, {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "NCI-N87", "model_type": "Cell Line-Derived Xenograft (CDX)", "experiment_type": "In Vivo", "endpoint_name": "Anti-tumor activity Dose", "endpoint_type": "Pharmacodynamics (PD)", "endpoint_value": "2", "endpoint_units": "mg/kg", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_26", "type": "endpoint"}, {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "NCI-N87", "model_type": "Cell Line-Derived Xenograft (CDX)", "experiment_type": "In Vivo", "endpoint_name": "Anti-tumor activity Dose", "endpoint_type": "Pharmacodynamics (PD)", "endpoint_value": "4", "endpoint_units": "mg/kg", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_27", "type": "endpoint"}, {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "NIBIO G016", "model_type": "Patient-Derived Xenograft (PDX)", "experiment_type": "In Vivo", "endpoint_name": "Anti-tumor activity Dose", "endpoint_type": "Pharmacodynamics (PD)", "endpoint_value": "3", "endpoint_units": "mg/kg", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_28", "type": "endpoint"}, {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "NIBIO G016", "model_type": "Patient-Derived Xenograft (PDX)", "experiment_type": "In Vivo", "endpoint_name": "Anti-tumor activity Dose", "endpoint_type": "Pharmacodynamics (PD)", "endpoint_value": "10", "endpoint_units": "mg/kg", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_29", "type": "endpoint"}, {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "ST225", "model_type": "Patient-Derived Xenograft (PDX)", "experiment_type": "In Vivo", "endpoint_name": "Anti-tumor activity Dose", "endpoint_type": "Pharmacodynamics (PD)", "endpoint_value": "10", "endpoint_units": "mg/kg", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_30", "type": "endpoint"}, {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "ST313", "model_type": "Patient-Derived Xenograft (PDX)", "experiment_type": "In Vivo", "endpoint_name": "Anti-tumor activity Dose", "endpoint_type": "Pharmacodynamics (PD)", "endpoint_value": "10", "endpoint_units": "mg/kg", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_31", "type": "endpoint"}, {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "ST565", "model_type": "Patient-Derived Xenograft (PDX)", "experiment_type": "In Vivo", "endpoint_name": "Anti-tumor activity Dose", "endpoint_type": "Pharmacodynamics (PD)", "endpoint_value": "10", "endpoint_units": "mg/kg", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_32", "type": "endpoint"}, {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "Cynomolgus Monkeys", "model_type": "Non-human primates", "experiment_type": "In Vivo", "endpoint_name": "Decreased Food Consumption", "endpoint_type": "Safety", "endpoint_value": "decreased food consumption (in 1 female at 78.8", "endpoint_units": "mg/kg)", "endpoint_timepoint": "NR", "endpoint_concentration": "78.8 mg/kg", "gt_id": "GT_ROW_33", "type": "endpoint"}, {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "Cynomolgus Monkeys", "model_type": "Non-human primates", "experiment_type": "In Vivo", "endpoint_name": "Pulmonary Toxicity", "endpoint_type": "Safety", "endpoint_value": "graded as slight or very slight", "endpoint_units": "NR", "endpoint_timepoint": "NR", "endpoint_concentration": "30 mg/kg", "gt_id": "GT_ROW_38", "type": "endpoint"}, {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "Cynomolgus Monkeys", "model_type": "Non-human primates", "experiment_type": "In Vivo", "endpoint_name": "Toxicity Dosing Regimen", "endpoint_type": "Safety", "endpoint_value": "every 3 weeks for 3 doses (up to 30", "endpoint_units": "mg/kg)", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_39", "type": "endpoint"}, {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "Rats", "model_type": "Rodent models", "experiment_type": "In Vivo", "endpoint_name": "Toxicity Dosing Regimen", "endpoint_type": "Safety", "endpoint_value": "every 3 weeks", "endpoint_units": "NR", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_40", "type": "endpoint"}, {"paper_id": "W2314772071", "adc_name": "DS-8201a", "model_name": "GCIY", "model_type": "Cell Line-Derived Xenograft (CDX)", "experiment_type": "In Vivo", "endpoint_name": "Tumor growth inhibition", "endpoint_type": "Efficacy", "endpoint_value": "not effective", "endpoint_units": "NR", "endpoint_timepoint": "NR", "endpoint_concentration": "NR", "gt_id": "GT_ROW_41", "type": "endpoint"}], "_metadata": {"format_version": "1.0", "writer_type": "JSONResultsWriter", "total_reasoning_tokens": 21824}}