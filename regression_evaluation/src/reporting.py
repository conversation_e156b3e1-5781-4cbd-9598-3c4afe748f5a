"""
Reporting script for generating aggregate evaluation metrics.

This script reads all evaluation JSON files from the data/evaluations directory,
computes aggregate statistics, and saves them to a new report.
"""

import os
import json
from pathlib import Path
from datetime import datetime
from collections import defaultdict
from typing import List, Dict, Any, Optional

def calculate_agreement_score(x: List[int], y: List[int]) -> Optional[float]:
    """
    Calculate the agreement score between two binary vectors.
    Formula: (agreements - disagreements) / total_items
    """
    if not x or not y or len(x) != len(y):
        return None
    
    agreements = sum(1 for i in range(len(x)) if x[i] == y[i])
    disagreements = len(x) - agreements
    
    return (agreements - disagreements) / len(x)

def generate_report(evaluations_dir: str, output_dir: str):
    """
    Generates an aggregate evaluation report from individual JSON files.

    Args:
        evaluations_dir: Directory containing the evaluation JSON files.
        output_dir: Directory where the final report will be saved.
    """
    print(f"Starting report generation from directory: {evaluations_dir}")
    
    all_files = [p for p in Path(evaluations_dir).glob("*.json")]
    if not all_files:
        print("No evaluation files found. Exiting.")
        return

    total_papers = 0
    overall_correlations = []
    weighted_correlation_data = []
    per_endpoint_data = defaultdict(lambda: {"eval": [], "human": [], "papers": set()})

    for file_path in all_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except (json.JSONDecodeError, IOError) as e:
            print(f"Warning: Could not read or parse {file_path}. Skipping. Error: {e}")
            continue

        total_papers += 1
        paper_id = data.get("paper_id", file_path.stem.replace("_evaluation", ""))
        
        # 1. Collect data for simple average
        if 'llm_vs_human_corr_overall' in data and data['llm_vs_human_corr_overall'] is not None:
            overall_correlations.append(data['llm_vs_human_corr_overall'])

        # 2. Collect data for weighted average
        num_endpoints = len(data.get('detailed_results', []))
        if num_endpoints > 0 and 'llm_vs_human_corr_overall' in data and data['llm_vs_human_corr_overall'] is not None:
            weighted_correlation_data.append({
                "score": data['llm_vs_human_corr_overall'],
                "weight": num_endpoints
            })

        # 3. Collect data for aggregated per-endpoint correlation
        for detail in data.get('detailed_results', []):
            endpoint_name = detail.get('extraction_row', {}).get('endpoint_name')
            if not endpoint_name:
                continue
            
            eval_bit = 1 if detail.get('classification') == "TP" else 0
            human_bit = 1 if detail.get('status') == "Correct" else 0
            
            per_endpoint_data[endpoint_name]["eval"].append(eval_bit)
            per_endpoint_data[endpoint_name]["human"].append(human_bit)
            per_endpoint_data[endpoint_name]["papers"].add(paper_id)

    # --- Calculations ---

    # Average overall correlation
    avg_overall_corr = sum(overall_correlations) / len(overall_correlations) if overall_correlations else None

    # Weighted average overall correlation
    total_weighted_score = sum(item['score'] * item['weight'] for item in weighted_correlation_data)
    total_weight = sum(item['weight'] for item in weighted_correlation_data)
    weighted_avg_corr = total_weighted_score / total_weight if total_weight > 0 else None

    # Aggregated per-endpoint correlation
    temp_endpoint_corr = []
    for ep_name, data in per_endpoint_data.items():
        correlation = calculate_agreement_score(data["eval"], data["human"])
        if correlation is not None:
            temp_endpoint_corr.append((ep_name, {
                "correlation": correlation,
                "papers": sorted(list(data["papers"]))
            }))

    # Sort by correlation score in ascending order
    temp_endpoint_corr.sort(key=lambda item: item[1]['correlation'])

    # Convert sorted list of tuples back to a dictionary
    aggregated_per_endpoint_corr = {k: v for k, v in temp_endpoint_corr}

    # --- Prepare and save the report ---
    
    report = {
        "report_generated_at": datetime.utcnow().isoformat(),
        "total_papers_analyzed": total_papers,
        "average_overall_correlation": avg_overall_corr,
        "weighted_average_overall_correlation": weighted_avg_corr,
        "aggregated_per_endpoint_correlation": aggregated_per_endpoint_corr
    }

    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = output_path / f"report_{timestamp}.json"

    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)

    print(f"Successfully generated report: {report_file}")
    print(json.dumps(report, indent=2))


def main():
    """Main function to run the reporting script."""
    # Assuming the script is run from the root of the project
    base_dir = Path(__file__).parent.parent
    evaluations_dir = base_dir / "data" / "evaluations"
    output_dir = base_dir / "data" / "evaluation_reports"
    
    generate_report(str(evaluations_dir), str(output_dir))

if __name__ == "__main__":
    main()
