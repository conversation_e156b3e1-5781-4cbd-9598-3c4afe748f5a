"""
Improved ADC evaluator with better separation of concerns and error handling.

This module implements the Evaluator interface with proper error handling,
logging, dependency injection, and metrics calculation.

TEST FILE: tests/test_evaluator.py
IMPORTANT: When modifying this file, ensure corresponding test cases in the test file
are updated or added to maintain test coverage and validate the changes.
"""

import math
from collections import defaultdict
from tqdm import tqdm
from typing import List, Optional, Set, Dict, Any
from models import *
from interfaces import Evaluator, Matcher
from matcher import ADCMatcher
from exceptions import EvaluationError, MatchingError
from utils.logging_utils import LoggerMixin, log_async_function_call
from utils.constants import Constants


class ADCEvaluator(LoggerMixin):
    """Evaluates ADC extraction results against ground truth."""
    
    def __init__(self, matcher: any, confidence_threshold: float = Constants.DEFAULT_CONFIDENCE_THRESHOLD):
        """Initialize the evaluator with a matcher."""
        self.matcher = ADCMatcher()
        self.confidence_threshold = confidence_threshold
        self.logger.info(f"Initialized ADC evaluator with confidence threshold: {self.confidence_threshold}")
    
    def evaluate(self, extraction_results: List[ExtractionRow], ground_truth: GroundTruthDocument, treat_additional_correct_as_incorrect: bool = False) -> EvaluationResult:
        """Evaluate extraction results against ground truth."""
        try:
            self.logger.info(f"Starting evaluation for paper {ground_truth.paper_id}: {len(extraction_results)} extractions vs {len(ground_truth.endpoints)} ground truth endpoints")
            
            if treat_additional_correct_as_incorrect:
                self.logger.info("Flag 'treat_additional_correct_as_incorrect' is enabled.")
                count = 0
                for res in extraction_results:
                    if res.source == "Additional" and res.status == "Correct":
                        res.status = "Incorrect"
                        count += 1
                if count > 0:
                    self.logger.info(f"Reclassified {count} 'Additional' and 'Correct' extractions to 'Incorrect'.")

            # Validate inputs
            self._validate_inputs(extraction_results, ground_truth)
            
            # Initialize evaluation state with available GT endpoints
            evaluation_state = self._initialize_evaluation_state(ground_truth.paper_id)
            available_gt_endpoints = list(ground_truth.endpoints)  # Create mutable copy
            
            # Assign persistent GT IDs once at the start of evaluation
            initial_gt_ids = {f"GT_ROW_{i+1}": gt_endpoint for i, gt_endpoint in enumerate(available_gt_endpoints)}
            for i, gt_endpoint in enumerate(available_gt_endpoints):
                gt_endpoint.gt_id = f"GT_ROW_{i+1}"
            
            self.logger.debug(f"Assigned GT IDs to {len(available_gt_endpoints)} endpoints: GT_ROW_1 through GT_ROW_{len(available_gt_endpoints)}")
            
            # Process each extraction endpoint with dynamic GT removal
            for idx, extracted_endpoint in enumerate(tqdm(extraction_results, desc="Evaluating Endpoints")):
                # Assertion: Verify that the gt_id of each remaining endpoint is still in the initial set
                for endpoint in available_gt_endpoints:
                    assert endpoint.gt_id in initial_gt_ids, f"GT ID {endpoint.gt_id} has changed or is new!"
                    assert initial_gt_ids[endpoint.gt_id] == endpoint, f"GT endpoint for {endpoint.gt_id} has been modified!"

                self._process_extraction_endpoint_with_removal(
                    extracted_endpoint, available_gt_endpoints, idx, evaluation_state
                )
            
            # Calculate final metrics with remaining unmatched GT endpoints
            return self._calculate_final_metrics_with_remaining(evaluation_state, available_gt_endpoints)
            
        except Exception as e:
            self.logger.error(f"Evaluation failed: {str(e)}")
            raise EvaluationError(f"Evaluation failed: {str(e)}")
    
    def _validate_inputs(self, extraction_results: List[ExtractionRow], ground_truth: GroundTruthDocument) -> None:
        """Validate evaluation inputs."""
        if not extraction_results:
            raise EvaluationError("Extraction results cannot be empty")
        
        if not ground_truth:
            raise EvaluationError("Ground truth document cannot be None")
        
        if not ground_truth.endpoints:
            raise EvaluationError("Ground truth must contain at least one endpoint")
        
        if not ground_truth.paper_id:
            raise EvaluationError("Ground truth must have a paper_id")
    
    def _initialize_evaluation_state(self, paper_id: str) -> Dict[str, Any]:
        """Initialize evaluation state tracking."""
        return {
            "paper_id": paper_id,
            "TP": 0,
            "FP": 0,
            "matched_gt_endpoints": set(),
            "detailed_results": []
        }
    
    def _process_extraction_endpoint_with_removal(
        self, 
        extracted_endpoint: ExtractionRow, 
        available_gt_endpoints: List[GTEndpoint], 
        idx: int,
        evaluation_state: Dict[str, Any]
    ) -> None:
        """Process a single extraction endpoint and remove matched GT endpoints from available pool."""
        try:
            self.logger.debug(f"Processing extraction endpoint {idx + 1}: {extracted_endpoint.endpoint_name} against {len(available_gt_endpoints)} available GT endpoints")
            
            # Match against currently available ground truth endpoints
            match_result = self.matcher.match_endpoint(extracted_endpoint, available_gt_endpoints)
            
            # Determine classification and handle GT removal
            classification = self._classify_and_remove_matched_gt(
                match_result, available_gt_endpoints, evaluation_state
            )
            
            # Store detailed result
            evaluation_state["detailed_results"].append(MatchResult(
                extraction_row=extracted_endpoint.model_dump(),
                llm_response=match_result,
                classification=classification,
                extraction_row_index=idx,
                comments=extracted_endpoint.comments,
                source=extracted_endpoint.source,
                status=extracted_endpoint.status,
                status_cat=extracted_endpoint.status_cat
            ))
            
            self.logger.debug(f"Endpoint {idx + 1} classified as {classification} (confidence: {match_result.confidence}). {len(available_gt_endpoints)} GT endpoints remaining.")
            
        except Exception as e:
            self.logger.error(f"Error processing extraction endpoint {idx + 1}: {str(e)}")
            # Continue processing other endpoints, but log the error
            evaluation_state["detailed_results"].append(MatchResult(
                extraction_row=extracted_endpoint.model_dump(),
                llm_response=LLMMatchResponse(
                    matched=False,
                    confidence=0.0,
                    reason=f"Processing error: {str(e)}",
                    reasoning=None,
                    reasoning_tokens=None
                ),
                classification=Constants.FALSE_POSITIVE,
                extraction_row_index=idx,
                comments=extracted_endpoint.comments,
                source=extracted_endpoint.source,
                status=extracted_endpoint.status,
                status_cat=extracted_endpoint.status_cat
            ))
            evaluation_state["FP"] += 1
    
    def _classify_and_remove_matched_gt(
        self, 
        match_result: LLMMatchResponse, 
        available_gt_endpoints: List[GTEndpoint], 
        evaluation_state: Dict[str, Any]
    ) -> str:
        """Classify match result and remove matched GT endpoint from available pool."""
        if match_result.matched and match_result.confidence >= self.confidence_threshold:
            # This is a True Positive - remove the matched GT endpoint
            if match_result.matched_gt_endpoint and match_result.matched_gt_endpoint in available_gt_endpoints:
                available_gt_endpoints.remove(match_result.matched_gt_endpoint)
                self.logger.debug(f"Removed matched GT endpoint: {match_result.matched_gt_endpoint.adc_name}/{match_result.matched_gt_endpoint.model_name}/{match_result.matched_gt_endpoint.endpoint_name}")
                evaluation_state["TP"] += 1
                evaluation_state["matched_gt_endpoints"].add(match_result.gt_id)
                return Constants.TRUE_POSITIVE
            else:
                self.logger.warning(f"TP classification but matched_gt_endpoint not found in available pool: {match_result.gt_id}")
                evaluation_state["FP"] += 1
                return Constants.FALSE_POSITIVE
        else:
            # This is a False Positive - extraction doesn't match any available GT
            evaluation_state["FP"] += 1
            return Constants.FALSE_POSITIVE
    
    def _calculate_agreement_score(self, x: List[int], y: List[int]) -> Optional[float]:
        """Calculate the agreement score between two binary vectors."""
        if not x or not y:
            return None
        
        agreements = sum(1 for i in range(len(x)) if x[i] == y[i])
        disagreements = len(x) - agreements
        
        return (agreements - disagreements) / len(x)

    def _calculate_correlation_metrics(self, detailed_results: List[MatchResult]) -> (Optional[float], Dict[str, Optional[float]]):
        """Calculate overall and per-endpoint agreement scores."""
        eval_bits = []
        human_bits = []
        per_ep = defaultdict(lambda: {"eval": [], "human": []})

        for row in detailed_results:
            eval_bit = 1 if row.classification == "TP" else 0
            
            # Special case for human annotations: If an extraction is marked "Correct" but the
            # source is "Additional", it means the extraction is valid but not present in the
            # ground truth. In this scenario, the evaluator is *expected* to classify it as a
            # False Positive (FP) because it cannot find a match. To avoid penalizing the
            # evaluator for this correct behavior, we treat this specific case as if the human
            # had marked it "Incorrect" (human_bit = 0) for the purpose of this correlation score.
            is_correct_and_from_gt = row.status == "Correct" and row.source == "GT"
            human_bit = 1 if is_correct_and_from_gt else 0
            
            eval_bits.append(eval_bit)
            human_bits.append(human_bit)

            # Use .get() to avoid errors if 'endpoint_name' is missing
            ep = row.extraction_row.get("endpoint_name")
            if ep:
                per_ep[ep]["eval"].append(eval_bit)
                per_ep[ep]["human"].append(human_bit)

        overall_score = self._calculate_agreement_score(eval_bits, human_bits)
        per_ep_score = {ep: self._calculate_agreement_score(v["eval"], v["human"]) for ep, v in per_ep.items()}
        
        return overall_score, per_ep_score

    def _calculate_final_metrics_with_remaining(self, evaluation_state: Dict[str, Any], remaining_gt_endpoints: List[GTEndpoint]) -> EvaluationResult:
        """Calculate final evaluation metrics using remaining unmatched GT endpoints."""
        TP = evaluation_state["TP"]
        FP = evaluation_state["FP"]
        
        # FN = remaining unmatched GT endpoints
        FN = len(remaining_gt_endpoints)
        
        # Convert remaining GT endpoints to unmatched items format
        unmatched_gt_items = []
        for endpoint in remaining_gt_endpoints:
            endpoint_dict = endpoint.model_dump()
            endpoint_dict["type"] = Constants.ROW_TYPE_ENDPOINT
            unmatched_gt_items.append(endpoint_dict)
        
        # Calculate metrics with proper rounding
        precision = round(TP / (TP + FP), Constants.PRECISION_DECIMALS) if (TP + FP) > 0 else 0.0
        recall = round(TP / (TP + FN), Constants.RECALL_DECIMALS) if (TP + FN) > 0 else 0.0
        f1 = round(2 * (precision * recall) / (precision + recall), Constants.F1_DECIMALS) if (precision + recall) > 0 else 0.0
        
        # Calculate correlation metrics
        overall_corr, per_ep_corr = self._calculate_correlation_metrics(evaluation_state["detailed_results"])

        # Log summary metrics
        self.logger.info(f"Evaluation completed with GT removal: TP={TP}, FP={FP}, FN={FN}, Precision={precision}, Recall={recall}, F1={f1}")
        
        return EvaluationResult(
            paper_id=evaluation_state["paper_id"],
            TP=TP, FP=FP, FN=FN,
            precision=precision, recall=recall, f1=f1,
            llm_vs_human_corr_overall=overall_corr,
            llm_vs_human_corr_by_endpoint=per_ep_corr,
            detailed_results=evaluation_state["detailed_results"],
            unmatched_gt_items=unmatched_gt_items
        )
    
    # Legacy method for backward compatibility
    async def _process_extraction_endpoint(
        self, 
        extracted_endpoint: ExtractionRow, 
        gt_endpoints: List[GTEndpoint], 
        idx: int,
        evaluation_state: Dict[str, Any]
    ) -> None:
        """Legacy method - delegates to new implementation with removal."""
        # For backward compatibility, create a copy so original list isn't modified
        available_gt_endpoints = list(gt_endpoints)
        await self._process_extraction_endpoint_with_removal(
            extracted_endpoint, available_gt_endpoints, idx, evaluation_state
        )
    
    def _classify_and_remove_matched_gt(
        self, 
        match_result: LLMMatchResponse, 
        available_gt_endpoints: List[GTEndpoint], 
        evaluation_state: Dict[str, Any]
    ) -> str:
        """Classify match result and remove matched GT endpoint from available pool."""
        if match_result.matched and match_result.confidence >= self.confidence_threshold:
            evaluation_state["TP"] += 1
            evaluation_state["matched_gt_endpoints"].add(match_result.gt_id)
            
            # Remove the matched GT endpoint from available pool
            if match_result.matched_gt_endpoint:
                try:
                    available_gt_endpoints.remove(match_result.matched_gt_endpoint)
                    self.logger.debug(f"Removed matched GT endpoint: {match_result.matched_gt_endpoint.adc_name}/{match_result.matched_gt_endpoint.model_name}/{match_result.matched_gt_endpoint.endpoint_name}")
                except ValueError:
                    self.logger.warning(f"Could not remove GT endpoint from available pool - endpoint not found")
            else:
                self.logger.warning(f"TP classification but no matched_gt_endpoint available for removal")
            
            return Constants.TRUE_POSITIVE
        else:
            evaluation_state["FP"] += 1
            return Constants.FALSE_POSITIVE
    
    # Legacy method for backward compatibility
    def _classify_match_result(self, match_result: LLMMatchResponse, evaluation_state: Dict[str, Any]) -> str:
        """Legacy method - classify match result as TP or FP without GT removal."""
        if match_result.matched and match_result.confidence >= self.confidence_threshold:
            evaluation_state["TP"] += 1
            evaluation_state["matched_gt_endpoints"].add(match_result.gt_id)
            return Constants.TRUE_POSITIVE
        else:
            evaluation_state["FP"] += 1
            return Constants.FALSE_POSITIVE
    
    def _calculate_final_metrics_with_remaining(self, evaluation_state: Dict[str, Any], remaining_gt_endpoints: List[GTEndpoint]) -> EvaluationResult:
        """Calculate final evaluation metrics using remaining unmatched GT endpoints."""
        TP = evaluation_state["TP"]
        FP = evaluation_state["FP"]
        
        # FN = remaining unmatched GT endpoints (more accurate than previous method)
        FN = len(remaining_gt_endpoints)
        
        # Convert remaining GT endpoints to unmatched items format
        unmatched_gt_items = []
        for endpoint in remaining_gt_endpoints:
            endpoint_dict = endpoint.model_dump()
            endpoint_dict["type"] = Constants.ROW_TYPE_ENDPOINT
            unmatched_gt_items.append(endpoint_dict)
        
        # Calculate metrics with proper rounding
        precision = round(TP / (TP + FP), Constants.PRECISION_DECIMALS) if (TP + FP) > 0 else 0.0
        recall = round(TP / (TP + FN), Constants.RECALL_DECIMALS) if (TP + FN) > 0 else 0.0
        f1 = round(2 * (precision * recall) / (precision + recall), Constants.F1_DECIMALS) if (precision + recall) > 0 else 0.0
        
        # Calculate correlation metrics
        overall_corr, per_ep_corr = self._calculate_correlation_metrics(evaluation_state["detailed_results"])

        # Log summary metrics
        self.logger.info(f"Evaluation completed with GT removal: TP={TP}, FP={FP}, FN={FN}, Precision={precision}, Recall={recall}, F1={f1}")
        
        return EvaluationResult(
            paper_id=evaluation_state["paper_id"],
            TP=TP, FP=FP, FN=FN,
            precision=precision, recall=recall, f1=f1,
            llm_vs_human_corr_overall=overall_corr,
            llm_vs_human_corr_by_endpoint=per_ep_corr,
            detailed_results=evaluation_state["detailed_results"],
            unmatched_gt_items=unmatched_gt_items
        )
    
    # Legacy method for backward compatibility
    def _calculate_final_metrics(self, evaluation_state: Dict[str, Any], ground_truth: GroundTruthDocument) -> EvaluationResult:
        """Legacy method - calculate final evaluation metrics without GT removal logic."""
        TP = evaluation_state["TP"]
        FP = evaluation_state["FP"]
        
        # Calculate FN: Ground truth endpoints not matched by any extraction
        total_gt_endpoints = len(ground_truth.endpoints)
        FN = total_gt_endpoints - len(evaluation_state["matched_gt_endpoints"])
        
        # Identify unmatched GT endpoints for detailed reporting
        unmatched_gt_items = self._identify_unmatched_gt_endpoints(
            ground_truth.endpoints, evaluation_state["matched_gt_endpoints"]
        )
        
        # Calculate metrics with proper rounding
        precision = round(TP / (TP + FP), Constants.PRECISION_DECIMALS) if (TP + FP) > 0 else 0.0
        recall = round(TP / (TP + FN), Constants.RECALL_DECIMALS) if (TP + FN) > 0 else 0.0
        f1 = round(2 * (precision * recall) / (precision + recall), Constants.F1_DECIMALS) if (precision + recall) > 0 else 0.0
        
        # Calculate correlation metrics
        overall_corr, per_ep_corr = self._calculate_correlation_metrics(evaluation_state["detailed_results"])

        # Log summary metrics
        self.logger.info(f"Evaluation completed: TP={TP}, FP={FP}, FN={FN}, Precision={precision}, Recall={recall}, F1={f1}")
        
        return EvaluationResult(
            paper_id=evaluation_state["paper_id"],
            TP=TP, FP=FP, FN=FN,
            precision=precision, recall=recall, f1=f1,
            llm_vs_human_corr_overall=overall_corr,
            llm_vs_human_corr_by_endpoint=per_ep_corr,
            detailed_results=evaluation_state["detailed_results"],
            unmatched_gt_items=unmatched_gt_items
        )
    
    def _identify_unmatched_gt_endpoints(self, gt_endpoints: List[GTEndpoint], matched_gt_endpoints: Set[str]) -> List[Dict[str, Any]]:
        """Identify ground truth endpoints that were not matched."""
        unmatched_gt_items = []
        
        for endpoint in gt_endpoints:
            endpoint_id = self._generate_endpoint_id(endpoint)
            if endpoint_id not in matched_gt_endpoints:
                endpoint_dict = endpoint.model_dump()
                endpoint_dict["type"] = Constants.ROW_TYPE_ENDPOINT
                unmatched_gt_items.append(endpoint_dict)
        
        self.logger.debug(f"Found {len(unmatched_gt_items)} unmatched ground truth endpoints")
        return unmatched_gt_items
    
    def _generate_endpoint_id(self, endpoint: GTEndpoint) -> str:
        """Generate a unique ID for an endpoint."""
        return f"endpoint_{endpoint.adc_name}_{endpoint.model_name}_{endpoint.endpoint_name}"
    
    def get_evaluation_summary(self, result: EvaluationResult) -> Dict[str, Any]:
        """Get a summary of evaluation results."""
        total_reasoning_tokens = sum(
            detail.llm_response.reasoning_tokens or 0 
            for detail in result.detailed_results
        )
        
        return {
            "paper_id": result.paper_id,
            "metrics": {
                "TP": result.TP,
                "FP": result.FP,
                "FN": result.FN,
                "precision": result.precision,
                "recall": result.recall,
                "f1": result.f1,
                "llm_vs_human_corr_overall": result.llm_vs_human_corr_overall,
                "llm_vs_human_corr_by_endpoint": result.llm_vs_human_corr_by_endpoint
            },
            "total_extractions": len(result.detailed_results),
            "total_gt_endpoints": result.TP + result.FN,
            "total_reasoning_tokens": total_reasoning_tokens,
            "confidence_threshold": self.confidence_threshold
        }