"""
File utility functions for the ADC evaluation system.

This module provides centralized file operations with proper error handling
and logging to avoid code duplication across the system.
"""

import json
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import logging

from exceptions import FileSystemError, PaperNotFoundError
from models import GroundTruthDocument, ExtractionRow, ExtractedEndpoint
from .constants import Constants


logger = logging.getLogger(__name__)


class FileUtils:
    """Utility class for file operations."""
    
    @staticmethod
    def get_paper_file_paths(paper_id: str, data_dir: str = Constants.DEFAULT_DATA_DIR) -> Dict[str, Path]:
        """Get all file paths for a given paper ID."""
        base_path = Path(data_dir)
        return {
            "ground_truth": base_path / Constants.GROUND_TRUTH_DIR / f"{paper_id}_gt{Constants.JSON_EXT}",
            "results": base_path / Constants.RESULTS_DIR / f"{paper_id}_results{Constants.JSON_EXT}",
            "evaluation": base_path / Constants.EVALUATIONS_DIR / f"{paper_id}_evaluation{Constants.JSON_EXT}"
        }
    
    @staticmethod
    def check_paper_files_exist(paper_id: str, data_dir: str = Constants.DEFAULT_DATA_DIR) -> Tuple[bool, List[str]]:
        """Check if all required files exist for a paper."""
        paths = FileUtils.get_paper_file_paths(paper_id, data_dir)
        missing_files = []
        
        for file_type, path in paths.items():
            if file_type != "evaluation" and not path.exists():  # evaluation file is created, not required
                missing_files.append(str(path))
        
        return len(missing_files) == 0, missing_files
    
    @staticmethod
    def load_json_file(file_path: Path) -> Dict[str, Any]:
        """Load and parse JSON file with error handling."""
        try:
            if not file_path.exists():
                raise FileSystemError(f"File not found: {file_path}")
            
            logger.debug(f"Loading JSON file: {file_path}")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
            
        except json.JSONDecodeError as e:
            raise FileSystemError(
                f"Invalid JSON in file {file_path}: {str(e)}", 
                {"file_path": str(file_path), "json_error": str(e)}
            )
        except Exception as e:
            raise FileSystemError(
                f"Error loading file {file_path}: {str(e)}", 
                {"file_path": str(file_path)}
            )
    
    @staticmethod
    def save_json_file(data: Dict[str, Any], file_path: Path) -> None:
        """Save data to JSON file with error handling."""
        try:
            logger.debug(f"Saving JSON file: {file_path}")
            
            # Ensure directory exists
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Successfully saved file: {file_path}")
            
        except Exception as e:
            raise FileSystemError(
                f"Error saving file {file_path}: {str(e)}", 
                {"file_path": str(file_path)}
            )
    
    @staticmethod
    def load_ground_truth(paper_id: str, data_dir: str = Constants.DEFAULT_DATA_DIR) -> GroundTruthDocument:
        """Load ground truth data for a specific paper."""
        paths = FileUtils.get_paper_file_paths(paper_id, data_dir)
        gt_path = paths["ground_truth"]
        
        if not gt_path.exists():
            raise PaperNotFoundError(paper_id, [str(gt_path)])
        
        try:
            data = FileUtils.load_json_file(gt_path)
            gt_doc = GroundTruthDocument(**data)
            
            # Validate paper_id matches (case-insensitive)
            if gt_doc.paper_id.lower() != paper_id.lower():
                raise FileSystemError(
                    f"Paper ID mismatch: file contains '{gt_doc.paper_id}', expected '{paper_id}'",
                    {"expected": paper_id, "found": gt_doc.paper_id}
                )
            
            logger.info(f"Loaded ground truth for paper {paper_id}: {len(gt_doc.endpoints)} endpoints")
            return gt_doc
            
        except Exception as e:
            if isinstance(e, (PaperNotFoundError, FileSystemError)):
                raise
            raise FileSystemError(f"Error loading ground truth for paper {paper_id}: {str(e)}")
    
    @staticmethod
    def load_extraction_results(paper_id: str, data_dir: str = Constants.DEFAULT_DATA_DIR) -> List[ExtractionRow]:
        """Load extraction results for a specific paper."""
        paths = FileUtils.get_paper_file_paths(paper_id, data_dir)
        results_path = paths["results"]
        
        if not results_path.exists():
            raise PaperNotFoundError(paper_id, [str(results_path)])
        
        try:
            data = FileUtils.load_json_file(results_path)
            
            # Parse and filter extraction rows
            extraction_rows = []
            for row_data in data:
                # Only process endpoint rows that have measurements
                if "endpoint_name" in row_data and row_data.get("endpoint_measurements"):
                    try:
                        # Add paper_id to the row if it's missing
                        if "paper_id" not in row_data:
                            row_data["paper_id"] = paper_id
                        extraction_rows.append(ExtractedEndpoint(**row_data))
                    except Exception as e:
                        logger.warning(f"Skipping a row for paper {paper_id} due to validation error: {e}")
                        continue
            
            logger.info(f"Loaded extraction results for paper {paper_id}: {len(extraction_rows)} endpoints")
            return extraction_rows
            
        except Exception as e:
            if isinstance(e, (PaperNotFoundError, FileSystemError)):
                raise
            raise FileSystemError(f"Error loading extraction results for paper {paper_id}: {str(e)}")
    
    @staticmethod
    async def save_evaluation_results(results: Dict[str, Any], paper_id: str, data_dir: str = Constants.DEFAULT_DATA_DIR) -> str:
        """Save evaluation results to file."""
        paths = FileUtils.get_paper_file_paths(paper_id, data_dir)
        evaluation_path = paths["evaluation"]
        
        await FileUtils.save_json_file(results, evaluation_path)
        return str(evaluation_path)