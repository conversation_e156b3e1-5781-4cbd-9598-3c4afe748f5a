"""
Constants used throughout the ADC evaluation system.

This module centralizes all configuration constants and magic values
to improve maintainability and avoid duplication.
"""



class Constants:
    """Central constants for the ADC evaluation system."""
    
    # Model Configuration
    PRIMARY_MODEL = "gpt-4.1"
    FALLBACK_MODEL = "o3-mini"
    
    # API Configuration
    MAX_RETRIES = 3
    TIMEOUT_SECONDS = 30
    DEFAULT_CONFIDENCE_THRESHOLD = 0.6
    
    # Token Limits
    MAX_REASONING_TOKENS = 2000
    MAX_FALLBACK_TOKENS = 1500
    
    # File Paths
    DEFAULT_DATA_DIR = "regression_evaluation/data"
    GROUND_TRUTH_DIR = "ground-truth"
    RESULTS_DIR = "extraction_results"
    EVALUATIONS_DIR = "evaluations"
    
    # File Extensions
    JSON_EXT = ".json"
    CSV_EXT = ".csv"
    
    # Data Validation
    REQUIRED_GT_FIELDS = ["paper_id", "adc_name", "model_name", "endpoint_name"]
    REQUIRED_EXTRACTION_FIELDS = ["paper_id", "type", "adc_name", "model_name", "endpoint_name"]
    
    
    # Logging
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_DATE_FORMAT = "%Y-%m-%d %H:%M:%S"
    
    # Evaluation Metrics
    PRECISION_DECIMALS = 3
    RECALL_DECIMALS = 3
    F1_DECIMALS = 3
    
    # Classification Types
    TRUE_POSITIVE = "TP"
    FALSE_POSITIVE = "FP"
    FALSE_NEGATIVE = "FN"
    
    # Row Types
    ROW_TYPE_ENDPOINT = "endpoint"
    ROW_TYPE_ADC = "adc"
    ROW_TYPE_MODEL = "model"