"""
Logging utility functions for the ADC evaluation system.

This module provides centralized logging configuration and utilities
to ensure consistent logging throughout the application.
"""

import logging
import sys
from typing import Optional
from .constants import Constants


def setup_logging(
    level: int = logging.INFO,
    format_string: Optional[str] = None,
    date_format: Optional[str] = None,
    log_file: Optional[str] = None
) -> None:
    """Set up logging configuration for the application."""
    
    # Use default formats if not provided
    if format_string is None:
        format_string = Constants.LOG_FORMAT
    if date_format is None:
        date_format = Constants.LOG_DATE_FORMAT
    
    # Create formatter
    formatter = logging.Formatter(format_string, date_format)
    
    # Set up handlers
    handlers = []
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    handlers.append(console_handler)
    
    # File handler (if specified)
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        handlers.append(file_handler)
    
    # Configure root logger
    logging.basicConfig(
        level=level,
        handlers=handlers,
        format=format_string,
        datefmt=date_format
    )
    
    # Set specific loggers to appropriate levels
    logging.getLogger("openai").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)


def get_logger(name: str) -> logging.Logger:
    """Get a logger with the specified name."""
    return logging.getLogger(name)


class LoggerMixin:
    """Mixin class to add logging capabilities to any class."""
    
    @property
    def logger(self) -> logging.Logger:
        """Get a logger for this class."""
        return logging.getLogger(self.__class__.__module__ + "." + self.__class__.__name__)


def log_function_call(func):
    """Decorator to log function calls with parameters and results."""
    def wrapper(*args, **kwargs):
        logger = logging.getLogger(func.__module__ + "." + func.__name__)
        
        # Log function call
        logger.debug(f"Calling {func.__name__} with args={args}, kwargs={kwargs}")
        
        try:
            result = func(*args, **kwargs)
            logger.debug(f"{func.__name__} completed successfully")
            return result
        except Exception as e:
            logger.error(f"{func.__name__} failed with error: {str(e)}")
            raise
    
    return wrapper


def log_async_function_call(func):
    """Decorator to log async function calls with parameters and results."""
    async def wrapper(*args, **kwargs):
        logger = logging.getLogger(func.__module__ + "." + func.__name__)
        
        # Log function call
        logger.debug(f"Calling async {func.__name__} with args={args}, kwargs={kwargs}")
        
        try:
            result = await func(*args, **kwargs)
            logger.debug(f"Async {func.__name__} completed successfully")
            return result
        except Exception as e:
            logger.error(f"Async {func.__name__} failed with error: {str(e)}")
            raise
    
    return wrapper