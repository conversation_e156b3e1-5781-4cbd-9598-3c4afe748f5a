"""
LLM client implementation with improved error handling and retry logic.

This module provides a robust LLM client that implements the LLMClient interface
with proper error handling, retry logic, and structured outputs support.

TEST FILES: tests/test_matcher.py, tests/test_matcher_structured.py
IMPORTANT: When modifying this file, ensure corresponding test cases in the test files
are updated or added to maintain test coverage and validate the changes.
"""

import logging
from typing import Any, Optional, Dict
from openai import OpenAI
from openai.types.chat import ChatCompletion

from interfaces import LLMClient
from config import get_openai_client, Config
from exceptions import LLMError, LLMTimeoutError, LLMQuotaExceededError, LLMResponseError
from utils.logging_utils import LoggerMixin, log_async_function_call
from utils.constants import Constants


class OpenAILLMClient(LLMClient, LoggerMixin):
    """OpenAI LLM client with structured outputs and error handling."""
    
    def __init__(self, client: Optional[Any] = None):
        """Initialize the LLM client."""
        self.client = client or get_openai_client()
        self.logger.info(f"Initialized OpenAI LLM client with models: {Config.PRIMARY_MODEL}, {Config.FALLBACK_MODEL}")
    
    def generate_structured_response(
        self, 
        system_prompt: str, 
        user_prompt: str, 
        response_model: type,
        max_tokens: int = Constants.MAX_REASONING_TOKENS
    ) -> Any:
        """Generate structured response using OpenAI's structured outputs."""
        try:
            self.logger.debug(f"Generating structured response with model: {Config.PRIMARY_MODEL}")
            
            # For gpt-4.1, use chat.completions.parse API (structured outputs)
            response = self.client.chat.completions.parse(
                model=Config.PRIMARY_MODEL,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                response_format=response_model,
                temperature=0,
                max_tokens=max_tokens
            )
            
            # Extract reasoning token usage (gpt-4.1 doesn't have reasoning tokens)
            reasoning_tokens = self._extract_reasoning_tokens(response)
            
            # Get parsed response
            parsed_response = response.choices[0].message.parsed
            
            # Handle case where parsing failed
            if parsed_response is None:
                self.logger.warning("Primary model parsing failed, falling back to o3-mini")
                return self.generate_chat_response(system_prompt, user_prompt, response_model, max_tokens)
            
            if hasattr(parsed_response, 'reasoning_tokens'):
                parsed_response.reasoning_tokens = reasoning_tokens
            if hasattr(parsed_response, 'language_model'):
                parsed_response.language_model = Config.PRIMARY_MODEL
            
            self.logger.info(f"Generated structured response with {reasoning_tokens or 0} reasoning tokens")
            return parsed_response
            
        except Exception as e:
            self.logger.error(f"Structured response generation failed: {str(e)}")
            
            # Only fallback for OpenAI API errors, not our parsing issues
            if self._is_openai_api_error(e):
                try:
                    self.logger.info("Falling back to chat completion API due to OpenAI API error")
                    return self.generate_chat_response(system_prompt, user_prompt, response_model, max_tokens)
                except Exception as fallback_error:
                    self.logger.error(f"Fallback also failed: {str(fallback_error)}")
                    raise self._convert_exception(e)
            else:
                # For our parsing issues, raise the error immediately
                self.logger.error(f"Client-side parsing error, not falling back: {str(e)}")
                raise self._convert_exception(e)
    
    def generate_chat_response(
        self, 
        system_prompt: str, 
        user_prompt: str, 
        response_model: type,
        max_tokens: int = Constants.MAX_FALLBACK_TOKENS
    ) -> Any:
        """Generate chat response as fallback method using o3-mini."""
        try:
            self.logger.debug(f"Generating chat response with model: {Config.FALLBACK_MODEL}")
            
            # For o3-mini, use standard chat.completions.create API (reasoning is built-in)
            response = self.client.chat.completions.create(
                model=Config.FALLBACK_MODEL,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                response_format={"type": "json_schema", "json_schema": {"name": "response", "schema": response_model.model_json_schema()}},
                max_completion_tokens=max_tokens
            )
            
            # Extract reasoning token usage
            reasoning_tokens = self._extract_reasoning_tokens(response)
            
            # Get JSON content and manually parse it
            json_content = response.choices[0].message.content
            
            # Handle case where content is None
            if json_content is None:
                raise LLMResponseError("Both primary and fallback models failed to parse response")
            
            # Parse JSON content using Pydantic
            try:
                import json
                self.logger.debug(f"o3-mini JSON content: {json_content}")
                json_data = json.loads(json_content)
                parsed_response = response_model(**json_data)
            except Exception as parse_error:
                self.logger.error(f"Failed to parse o3-mini JSON response: {str(parse_error)}")
                self.logger.error(f"Raw JSON content: {repr(json_content)}")
                raise LLMResponseError("Both primary and fallback models failed to parse response")
            
            if hasattr(parsed_response, 'reasoning_tokens'):
                parsed_response.reasoning_tokens = reasoning_tokens
            if hasattr(parsed_response, 'language_model'):
                parsed_response.language_model = Config.FALLBACK_MODEL
            
            self.logger.info(f"Generated chat response successfully with {reasoning_tokens or 0} reasoning tokens")
            return parsed_response
            
        except Exception as e:
            self.logger.error(f"Chat response generation failed: {str(e)}")
            raise self._convert_exception(e)
    
    
    def _is_retryable_error(self, error: Exception) -> bool:
        """Check if an error is retryable."""
        error_str = str(error).lower()
        
        # Rate limiting and server errors are retryable
        retryable_indicators = [
            "rate limit",
            "429",
            "502",
            "503",
            "504",
            "internal server error",
            "service unavailable",
            "gateway timeout"
        ]
        
        return any(indicator in error_str for indicator in retryable_indicators)
    
    def _is_openai_api_error(self, error: Exception) -> bool:
        """Check if an error is from OpenAI API (not our parsing)."""
        error_str = str(error).lower()
        
        # OpenAI API errors that warrant fallback
        openai_api_indicators = [
            "rate limit",
            "429",
            "502",
            "503",
            "504",
            "internal server error",
            "service unavailable",
            "gateway timeout",
            "connection error",
            "timeout",
            "api key",
            "unauthorized",
            "forbidden",
            "model not found",
            "invalid model"
        ]
        
        # Our parsing errors should NOT trigger fallback
        client_side_indicators = [
            "validation error",
            "pydantic",
            "json_invalid",
            "hasattr",
            "attribute error",
            "key error",
            "type error"
        ]
        
        # If it's clearly a client-side error, don't fallback
        if any(indicator in error_str for indicator in client_side_indicators):
            return False
        
        # If it's clearly an OpenAI API error, fallback
        return any(indicator in error_str for indicator in openai_api_indicators)
    
    def _extract_reasoning_tokens(self, response: Any) -> Optional[int]:
        """Extract reasoning token count from response."""
        try:
            if hasattr(response, 'usage') and response.usage:
                # For o3-mini chat.completions.create API
                if hasattr(response.usage, 'completion_tokens_details'):
                    details = response.usage.completion_tokens_details
                    if hasattr(details, 'reasoning_tokens'):
                        return details.reasoning_tokens
                # Fallback for other response formats
                if hasattr(response.usage, 'output_tokens_details'):
                    return getattr(response.usage.output_tokens_details, 'reasoning_tokens', None)
        except Exception as e:
            self.logger.debug(f"Could not extract reasoning tokens: {str(e)}")
        return None
    
    def _convert_exception(self, error: Exception) -> LLMError:
        """Convert generic exceptions to specific LLM exceptions."""
        error_str = str(error).lower()
        
        if "timeout" in error_str:
            return LLMTimeoutError(f"LLM request timed out: {str(error)}")
        elif "rate limit" in error_str or "429" in error_str:
            return LLMQuotaExceededError(f"LLM quota exceeded: {str(error)}")
        elif "parse" in error_str or "json" in error_str:
            return LLMResponseError(f"LLM response parsing failed: {str(error)}")
        else:
            return LLMError(f"LLM error: {str(error)}")


class MockLLMClient(LLMClient, LoggerMixin):
    """Mock LLM client for testing purposes."""
    
    def __init__(self, responses: Optional[Dict[str, Any]] = None):
        """Initialize mock client with predefined responses."""
        self.responses = responses or {}
        self.call_count = 0
        self.logger.info("Initialized mock LLM client")
    
    def generate_structured_response(
        self, 
        system_prompt: str, 
        user_prompt: str, 
        response_model: type,
        max_tokens: int = Constants.MAX_REASONING_TOKENS
    ) -> Any:
        """Generate mock structured response."""
        self.call_count += 1
        self.logger.debug(f"Mock structured response call #{self.call_count}")
        
        # Return predefined response or default
        if "structured" in self.responses:
            return self.responses["structured"]
        
        # Default mock response using standardized GT_ROW format
        return response_model(
            matched=True,
            gt_id="GT_ROW_1",  # Use standardized GT ID format
            matched_gt_endpoint=None,  # Will be filled by matcher
            confidence=0.95,
            reason="Mock response",
            reasoning="Mock reasoning",
            reasoning_tokens=100,
            language_model="mock-llm"
        )
    
    def generate_chat_response(
        self, 
        system_prompt: str, 
        user_prompt: str, 
        response_model: type,
        max_tokens: int = Constants.MAX_FALLBACK_TOKENS
    ) -> Any:
        """Generate mock chat response."""
        self.call_count += 1
        self.logger.debug(f"Mock chat response call #{self.call_count}")
        
        # Return predefined response or default
        if "chat" in self.responses:
            return self.responses["chat"]
        
        # Default mock response for fallback (no match)
        return response_model(
            matched=False,
            gt_id=None,  # No GT ID when no match
            matched_gt_endpoint=None,
            confidence=0.0,
            reason="Mock fallback response",
            reasoning="Mock fallback reasoning",
            reasoning_tokens=None,
            language_model="mock-llm-fallback"
        )