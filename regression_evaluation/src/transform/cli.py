"""
Simplified CSV to JSON converter for ADC ground truth data.
"""

import pandas as pd
import json
import logging
from pathlib import Path
import sys
import argparse
import numpy as np

# Ensure the script can find the 'src' directory
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.models import GroundTruthDocument, GTEndpoint

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def convert_csv_to_json(csv_path: str, output_dir: str):
    """
    Reads the ground truth CSV, groups by paper ID, and writes a JSON file for each paper.
    """
    try:
        df = pd.read_csv(csv_path, encoding='utf-8-sig')
        logging.info(f"Successfully loaded {csv_path}")
    except FileNotFoundError:
        logging.error(f"Error: The file was not found at {csv_path}")
        return

    # Replace NaN values with None for clean JSON output
    df = df.replace({np.nan: None})
    
    # Clean up column names by stripping leading/trailing whitespace
    df.columns = df.columns.str.strip()

    # Group by the paper ID
    for paper_id, group in df.groupby('id'):
        paper_id_str = str(paper_id).strip()
        logging.info(f"Processing paper: {paper_id_str}")

        endpoints = []
        for _, row in group.iterrows():
            # Create a dictionary from the row, handling potential missing columns
            row_dict = row.to_dict()
            
            endpoint_data = {
                "paper_id": paper_id_str,
                "adc_name": row_dict.get("adc_name"),
                "model_name": row_dict.get("model_name"),
                "model_type": row_dict.get("model_type"),
                "experiment_type": row_dict.get("experiment_type"),
                "endpoint_name": row_dict.get("endpoint_name"),
                "endpoint_type": row_dict.get("endpoint_type"),
                "endpoint_value": row_dict.get("endpoint_value"),
                "endpoint_units": row_dict.get("endpoint_units"),
                "endpoint_timepoint": row_dict.get("endpoint_timepoint"),
                "endpoint_concentration": row_dict.get("endpoint_concentration"),
            }
            endpoints.append(GTEndpoint(**endpoint_data))

        ground_truth_doc = GroundTruthDocument(
            paper_id=paper_id_str,
            endpoints=endpoints
        )

        # Save the JSON file
        output_path = Path(output_dir) / f"{paper_id_str}_gt.json"
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(ground_truth_doc.model_dump(), f, indent=2, ensure_ascii=False)
        
        logging.info(f"Successfully generated {output_path}")

def main():
    parser = argparse.ArgumentParser(description="Convert ADC ground truth CSV to JSON files.")
    parser.add_argument(
        "--csv-path",
        default="data/raw/AI_POC_all40_groundtruth_data_BIKG(Endpoints).csv",
        help="Path to the Endpoints CSV file."
    )
    parser.add_argument(
        "--output-dir",
        default="data/ground-truth/",
        help="Directory to save the generated JSON files."
    )
    args = parser.parse_args()

    convert_csv_to_json(args.csv_path, args.output_dir)

if __name__ == "__main__":
    main()
