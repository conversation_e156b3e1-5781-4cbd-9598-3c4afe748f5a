"""
CSV to JSON converter for ADC evaluation data.

This module provides functionality to convert CSV files containing ADC extraction
results into the JSON format required by the evaluation system.

TEST FILE: tests/test_csv_converter.py
IMPORTANT: When modifying this file, ensure corresponding test cases in the test file
are updated or added to maintain test coverage and validate the changes.
"""

import pandas as pd
import json
import re
from pathlib import Path
from typing import Dict, List, Optional, Any
from src.utils.logging_utils import get_logger

logger = get_logger(__name__)

class CSVConverter:
    """Main CSV to JSON converter."""
    
    def __init__(self, file_path: str):
        self.file_path = Path(file_path).resolve()
        self.endpoints_df: Optional[pd.DataFrame] = None
    
    def load_csv_files(self):
        """Load the specified CSV file."""
        try:
            if not self.file_path.is_file():
                raise FileNotFoundError(f"No CSV file found at {self.file_path}")
            
            self.endpoints_df = pd.read_csv(self.file_path, encoding='utf-8-sig')
            logger.info(f"Loaded {len(self.endpoints_df)} endpoint records from {self.file_path.name}")

        except Exception as e:
            logger.error(f"Error loading CSV file: {e}")
            raise
    
    def process_paper(self, paper_id: str) -> List[Dict[str, Any]]:
        """Process a single paper and return JSON format data."""
        if self.endpoints_df is None:
            raise ValueError("CSV files not loaded. Call load_csv_files() first.")
            
        # Filter endpoints for this paper
        paper_endpoints = self.endpoints_df[self.endpoints_df['id'] == paper_id]
        
        if paper_endpoints.empty:
            logger.warning(f"No endpoints found for paper {paper_id}")
            return []
        
        results = []
        for idx, row in paper_endpoints.iterrows():
            try:
                # Generate unique measurement ID
                measurement_id = f"{paper_id}_{idx}"
                
                # Convert row to JSON format
                json_record = self.convert_to_json_format(row, measurement_id)
                results.append(json_record)
                
            except Exception as e:
                logger.error(f"Error processing row {idx} for paper {paper_id}: {e}")
                continue
        
        logger.info(f"Processed {len(results)} endpoints for paper {paper_id}")
        return results
    
    def convert_to_json_format(self, row: pd.Series, measurement_id: str) -> Dict[str, Any]:
        """Convert a single CSV row to JSON format."""
        # Create base structure
        json_record = {
            "paper_id": row['id'],
            "type": "endpoint",
            "adc_name": self._clean_value(row['adc_name']),
            "model_name": self._clean_value(row['model_name']),
            "model_type": self._clean_value(row.get('model_type', '')),
            "experiment_type": self._clean_value(row.get('experiment_type', '')),
            "endpoint_name": self._clean_value(row['endpoint_name']),
            "endpoint_type": self._clean_value(row.get('endpoint_type', 'Unknown')),
            "comments": self._clean_value(row.get('Comments', '')),
            "source": self._clean_value(row.get('Source ', '')),
            "status": self._clean_value(row.get('Status', '')),
            "status_cat": self._clean_value(row.get('Status_cat', '')),
            "endpoint_measurements": []
        }
        
        # Create measurement object
        measurement = {
            "measurement_id": measurement_id,
            "value": self._create_value_object(row['measured_value'], row.get('endpoint_citations', '')),
            "time_point": self._create_timepoint_object(row.get('measured_timepoint', '')),
            "dose": self._create_dose_object(row.get('measured_concentration', ''))
        }
        
        # Remove None values
        measurement = {k: v for k, v in measurement.items() if v is not None}
        json_record["endpoint_measurements"].append(measurement)
        
        return json_record
    
    def _clean_value(self, value: Any) -> str:
        """Clean and normalize a value."""
        if pd.isna(value) or value == 'NR' or value == '':
            return ''
        return str(value).strip()
    
    def _create_value_object(self, value: Any, citation: str) -> Optional[Dict[str, Any]]:
        """Create a value object with value, unit, and citation."""
        if pd.isna(value) or value == 'NR' or value == '':
            return None
            
        value_str = str(value).strip()
        
        # Try to parse numeric value and unit
        numeric_value, unit = self._parse_numeric_value(value_str)
        
        return {
            "value": numeric_value if numeric_value is not None else value_str,
            "unit": unit,
            "citation": self._clean_value(citation)
        }
    
    def _create_timepoint_object(self, timepoint: Any) -> Optional[Dict[str, Any]]:
        """Create a timepoint object."""
        if pd.isna(timepoint) or timepoint == 'NR' or timepoint == '':
            return None
            
        timepoint_str = str(timepoint).strip()
        
        # Parse timepoint (e.g., "72h", "24 hours", "3 days")
        numeric_value, unit = self._parse_time_value(timepoint_str)
        
        return {
            "value": numeric_value if numeric_value is not None else timepoint_str,
            "unit": unit or "unknown",
            "citation": "extracted from timepoint field"
        }
    
    def _create_dose_object(self, dose: Any) -> Optional[Dict[str, Any]]:
        """Create a dose object."""
        if pd.isna(dose) or dose == 'NR' or dose == '':
            return None
            
        dose_str = str(dose).strip()
        
        # Parse dose (e.g., "1 mg/kg", "5 μg/ml")
        numeric_value, unit = self._parse_numeric_value(dose_str)
        
        return {
            "value": numeric_value if numeric_value is not None else dose_str,
            "unit": unit or "unknown",
            "citation": "extracted from concentration field"
        }
    
    def _parse_numeric_value(self, value_str: str) -> tuple[Optional[float], Optional[str]]:
        """Parse numeric value and unit from string."""
        # Common patterns for numeric values with units
        patterns = [
            r'([<>≤≥]?\s*\d+\.?\d*)\s*([a-zA-Z%/]+)',  # e.g., "0.5 nM", ">10 mg/kg"
            r'([<>≤≥]?\s*\d+\.?\d*)',  # Just number
        ]
        
        for pattern in patterns:
            match = re.search(pattern, value_str)
            if match:
                try:
                    num_str = match.group(1).strip()
                    unit = match.group(2).strip() if len(match.groups()) > 1 else None
                    
                    # Handle inequalities
                    if any(op in num_str for op in ['<', '>', '≤', '≥']):
                        return num_str, unit
                    
                    # Convert to float
                    numeric_value = float(num_str)
                    return numeric_value, unit
                except (ValueError, IndexError):
                    continue
        
        return None, None
    
    def _parse_time_value(self, time_str: str) -> tuple[Optional[float], Optional[str]]:
        """Parse time value and unit."""
        # Common time patterns
        patterns = [
            (r'(\d+\.?\d*)\s*h(ours?)?', 'hours'),
            (r'(\d+\.?\d*)\s*d(ays?)?', 'days'),
            (r'(\d+\.?\d*)\s*m(in(ute)?s?)?', 'minutes'),
            (r'(\d+\.?\d*)\s*w(eeks?)?', 'weeks'),
            (r'(\d+\.?\d*)\s*s(ec(ond)?s?)?', 'seconds'),
        ]
        
        for pattern, unit in patterns:
            match = re.search(pattern, time_str, re.IGNORECASE)
            if match:
                try:
                    value = float(match.group(1))
                    return value, unit
                except ValueError:
                    continue
        
        return None, None
    
    def convert_all_papers(self) -> Dict[str, List[Dict[str, Any]]]:
        """Convert all papers to JSON format."""
        if self.endpoints_df is None:
            raise ValueError("CSV files not loaded. Call load_csv_files() first.")
            
        # Get unique paper IDs
        paper_ids = self.endpoints_df['id'].unique()
        
        results = {}
        for paper_id in paper_ids:
            try:
                paper_data = self.process_paper(paper_id)
                if paper_data:
                    results[paper_id] = paper_data
                    
            except Exception as e:
                logger.error(f"Error processing paper {paper_id}: {e}")
                continue
        
        logger.info(f"Converted {len(results)} papers successfully")
        return results
    
    def save_paper_json(self, paper_id: str, output_dir: str):
        """Save a single paper's data to JSON file."""
        paper_data = self.process_paper(paper_id)
        
        if not paper_data:
            logger.warning(f"No data found for paper {paper_id}")
            return
            
        output_path = Path(output_dir) / f"{paper_id}_results.json"
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(paper_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Saved {len(paper_data)} endpoints for paper {paper_id} to {output_path}")
    
    def save_all_papers_json(self, output_dir: str):
        """Save all papers' data to individual JSON files."""
        if self.endpoints_df is None:
            raise ValueError("CSV files not loaded. Call load_csv_files() first.")
            
        paper_ids = self.endpoints_df['id'].unique()
        
        for paper_id in paper_ids:
            try:
                self.save_paper_json(paper_id, output_dir)
            except Exception as e:
                logger.error(f"Error saving paper {paper_id}: {e}")
                continue
        
        logger.info(f"Saved {len(paper_ids)} papers to {output_dir}")