import argparse
import subprocess
import os
import time

# Parse command line arguments
parser = argparse.ArgumentParser(description="Run extraction and evaluation for given paper IDs.")
parser.add_argument('ids', nargs='+', help='List of paper IDs to process')
args = parser.parse_args()

start_time = time.time()
results = []

for paper_id in args.ids:
    input_file = f"output/{paper_id}.md"
    output_dir = "regression_evaluation/data/extraction_results/"
    extraction_cmd = [
        "python", "src/extraction_pipeline_parallel_strurcture.py",
        "--input-file", input_file,
        "--output-dir", output_dir
    ]
    print(f"Running extraction for {paper_id}...")
    extract_start = time.time()
    try:
        subprocess.run(extraction_cmd, check=True)
        extract_status = 'success'
    except subprocess.CalledProcessError as e:
        print(f"Extraction failed for {paper_id}: {e}")
        extract_status = 'failed'
    extract_end = time.time()
    extraction_time = extract_end - extract_start
    results.append((paper_id, 'extraction', extraction_time, extract_status))

    eval_cmd = [
        "python", "regression_evaluation/src/main.py",
        "--paper-id", paper_id,
        "--treat-additional-correct-as-incorrect"
    ]
    print(f"Running evaluation for {paper_id}...")
    eval_start = time.time()
    try:
        subprocess.run(eval_cmd, check=True)
        eval_status = 'success'
    except subprocess.CalledProcessError as e:
        print(f"Evaluation failed for {paper_id}: {e}")
        eval_status = 'failed'
    eval_end = time.time()
    eval_time = eval_end - eval_start
    results.append((paper_id, 'evaluation', eval_time, eval_status))

    # Print timing for this paper_id as soon as it's done
    print(f"{paper_id} - extraction: {extraction_time:.2f} seconds ({extract_status})")
    print(f"{paper_id} - evaluation: {eval_time:.2f} seconds ({eval_status})")

# Print all timings at the end
print("\nTiming summary:")
for paper_id, stage, t, status in results:
    print(f"{paper_id} - {stage}: {t:.2f} seconds ({status})")

end_time = time.time()
total_time = end_time - start_time
print(f"Total time for all runs: {total_time:.2f} seconds")
