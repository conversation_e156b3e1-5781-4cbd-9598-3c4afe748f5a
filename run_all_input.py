import os
import glob
import subprocess
import argparse
import shutil
import sys

# --- Add regression_evaluation/src to the Python path ---
EVALUATION_SRC_DIR = os.path.abspath(os.path.join('regression_evaluation', 'src'))
if EVALUATION_SRC_DIR not in sys.path:
    sys.path.append(EVALUATION_SRC_DIR)

# --- Import from the evaluation script ---
from main import ADCEvaluationApp, configure_logging
from results_writer import ConsoleResultsWriter, JSONResultsWriter, MultiResultsWriter


# --- Argument Parsing ---
parser = argparse.ArgumentParser(description="Run extraction pipeline and optionally trigger evaluation.")
parser.add_argument(
    '--run-evaluations',
    action='store_true',
    help="If set, the script will run evaluations for papers with ground truth files."
)
args = parser.parse_args()

# --- Main Script ---

# Get all .md files in ./output_4.1/
md_files = glob.glob(os.path.join('output_4.1', '*.md'))

if not md_files:
    print('No .md files found in ./output_4.1/')
    exit(0)

output_dir = 'output'
evaluation_dir = 'regression_evaluation'
ground_truth_dir = os.path.join(evaluation_dir, 'data', 'ground-truth')
eval_input_dir = os.path.join(evaluation_dir, 'data', 'extraction_results')

# --- Run Extraction ---
for md_file in md_files:
    print(f'Processing {md_file}...')
    cmd = [
        os.path.join('venv', 'Scripts', 'python.exe'),
        'src/extraction_pipeline_parallel_strurcture.py',
        '--input-file',
        md_file,
        '--output-dir',
        output_dir
    ]
    result = subprocess.run(cmd)
    if result.returncode != 0:
        print(f'Error processing {md_file}, exited with code {result.returncode}')
        break
else:
    print("\nExtraction process completed.")

# --- Run Evaluation ---
if args.run_evaluations:
    print("\n--run-evaluations flag is set. Starting evaluation process...")
    
    extraction_files = glob.glob(os.path.join(output_dir, '*_results.json'))
    extraction_ids = {os.path.basename(f).split('_results.json')[0] for f in extraction_files}
    
    ground_truth_files = glob.glob(os.path.join(ground_truth_dir, '*_gt.json'))
    ground_truth_ids = {os.path.basename(f).split('_gt.json')[0] for f in ground_truth_files}
    
    evaluation_candidates = sorted(list(extraction_ids.intersection(ground_truth_ids)))
    
    if evaluation_candidates:
        print(f"\nFound {len(evaluation_candidates)} papers for evaluation.")
        
        print(f"Preparing {len(evaluation_candidates)} extraction results for evaluation...")
        os.makedirs(eval_input_dir, exist_ok=True)
        for paper_id in evaluation_candidates:
            source_file = os.path.join(output_dir, f"{paper_id}_results.json")
            destination_file = os.path.join(eval_input_dir, f"{paper_id}_results.json")
            if os.path.exists(source_file):
                shutil.copy(source_file, destination_file)
        print("Preparation complete.")

        print("\nConfiguring evaluation app...")
        configure_logging(verbose=True)
        
        writers = [
            ConsoleResultsWriter(verbose=True),
            JSONResultsWriter(os.path.join(evaluation_dir, 'data'))
        ]
        
        app = ADCEvaluationApp(
            results_writer=MultiResultsWriter(writers)
        )
        
        print("Starting evaluation for each candidate...")
        for paper_id in evaluation_candidates:
            try:
                print(f"\n--- Evaluating paper: {paper_id} ---")
                results = app.run_evaluation(paper_id, data_dir=os.path.join(evaluation_dir, 'data'))
                output_path = app.save_results(results)
                print(f"\nEvaluation for {paper_id} completed successfully!")
                print(f"Results: TP={results.TP}, FP={results.FP}, FN={results.FN}")
                print(f"F1 Score: {results.f1:.3f}")
                print(f"Saved to: {output_path}")
            except Exception as e:
                print(f"An error occurred during evaluation for {paper_id}: {e}")

    else:
        print("\nNo papers found with both extraction results and ground truth.")

print('\nDone.')
